# processors/relations/release_track_link_processor.py

from typing import Dict, List
from sqlalchemy.orm import Session
import logging

from ddex_processor.models.generated import ReleaseTrackLinks
from ddex_processor.extractors.relations.release_track_link_extractor import ReleaseTrackLinkExtractor


class ReleaseTrackLinkProcessor:
    """Processor for release-track many-to-many relationships"""

    def __init__(self, update_existing: bool = False):
        self.update_existing = update_existing
        self.logger = logging.getLogger(self.__class__.__name__)

    def process_release_track_links(
        self,
        session: Session,
        ddex_data: Dict,
        entity_maps: Dict
    ) -> bool:
        """Process all release-track links"""
        self.logger.debug("Processing release-track links...")

        try:
            # Extract link data
            extractor = ReleaseTrackLinkExtractor(ddex_data)
            link_data_list = extractor.extract()

            if not link_data_list:
                self.logger.debug("No release-track links found")
                return True

            # Process each link
            processed_count = 0
            skipped_count = 0

            for link_data in link_data_list:
                if self._process_single_link(session, link_data, entity_maps):
                    processed_count += 1
                else:
                    skipped_count += 1

            self.logger.info(
                f"Processed {processed_count} release-track links, "
                f"skipped {skipped_count} due to missing entities"
            )

            return True

        except Exception as e:
            self.logger.error(
                f"Error processing release-track links: {str(e)}")
            return False

    def _process_single_link(
        self,
        session: Session,
        link_data: Dict,
        entity_maps: Dict
    ) -> bool:
        """Process a single release-track link"""

        # Look up the entities
        release = entity_maps.get('releases', {}).get(
            link_data['release_business_key'])
        track = entity_maps.get('tracks', {}).get(
            link_data['track_business_key'])

        # Validate that both entities exist
        if not release:
            self.logger.warning(
                f"Release not found for business key: {link_data['release_business_key']}"
            )
            return False

        if not track:
            self.logger.warning(
                f"Track not found for business key: {link_data['track_business_key']}"
            )
            return False

        # Create or update the link
        return self._upsert_link(session, release, track, link_data)

    def _upsert_link(
        self,
        session: Session,
        release,
        track,
        link_data: Dict
    ) -> bool:
        """Create or update a release-track link"""
        try:
            # Check if link already exists
            existing_link = session.query(ReleaseTrackLinks).filter_by(
                release_id=release.id,
                track_id=track.id,
                sequence=link_data['sequence'],
                volume=link_data['volume']
            ).first()

            if existing_link:
                if self.update_existing:
                    # Update existing link (though for this table, there's not much to update)
                    self.logger.debug(
                        f"Link already exists: Release {release.id} -> Track {track.id} "
                        f"(seq: {link_data['sequence']}, vol: {link_data['volume']})"
                    )
                    return True
                else:
                    # Skip existing link
                    self.logger.debug(
                        f"Skipping existing link: Release {release.id} -> Track {track.id}"
                    )
                    return True
            else:
                # Create new link
                new_link = ReleaseTrackLinks(
                    release_id=release.id,
                    track_id=track.id,
                    sequence=link_data['sequence'],
                    volume=link_data['volume']
                )

                session.add(new_link)

                self.logger.debug(
                    f"Created link: Release {release.id} -> Track {track.id} "
                    f"(seq: {link_data['sequence']}, vol: {link_data['volume']})"
                )
                return True

        except Exception as e:
            self.logger.error(
                f"Error creating release-track link: {str(e)}"
            )
            return False

    def clear_release_track_links(self, session: Session, release_id: str):
        """Clear all track links for a specific release (useful for full replacement)"""
        try:
            deleted_count = session.query(ReleaseTrackLinks).filter_by(
                release_id=release_id
            ).delete()

            self.logger.info(
                f"Cleared {deleted_count} track links for release {release_id}")

        except Exception as e:
            self.logger.error(f"Error clearing release track links: {str(e)}")

    def get_links_for_release(self, session: Session, release_id: str) -> List[ReleaseTrackLinks]:
        """Get all track links for a specific release"""
        try:
            links = session.query(ReleaseTrackLinks).filter_by(
                release_id=release_id
            ).order_by(ReleaseTrackLinks.volume, ReleaseTrackLinks.sequence).all()

            return links

        except Exception as e:
            self.logger.error(f"Error getting release track links: {str(e)}")
            return []

    def validate_link_data(self, link_data: Dict) -> bool:
        """Validate that link data has all required fields"""
        required_fields = ['release_business_key',
                           'track_business_key', 'sequence', 'volume']

        for field in required_fields:
            if field not in link_data or link_data[field] is None:
                self.logger.warning(
                    f"Missing required field '{field}' in link data")
                return False

        # Validate sequence and volume are positive integers
        try:
            sequence = int(link_data['sequence'])
            volume = int(link_data['volume'])

            if sequence <= 0 or volume <= 0:
                self.logger.warning(
                    "Sequence and volume must be positive integers")
                return False

        except (ValueError, TypeError):
            self.logger.warning("Sequence and volume must be valid integers")
            return False

        return True
