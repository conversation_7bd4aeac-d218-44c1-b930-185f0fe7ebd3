# extractors/relations/release_track_link_extractor.py

from typing import List, Dict, Any
from ..base_extractor import BaseExtractor


class ReleaseTrackLinkExtractor(BaseExtractor):
    """Extract release-track linking data from DDEX JSON"""

    def extract(self) -> List[Dict[str, Any]]:
        """Extract all release-track links from DDEX data"""
        self.logger.info("Extracting release-track links")
        links = []

        releases = self._safe_get(self.ddex_data, 'ReleaseList.Release', [])

        for release in self._ensure_list(releases):
            if release.get('@IsMainRelease') == 'true':
                release_links = self._extract_links_for_release(release)
                links.extend(release_links)

        self.logger.info(f"Extracted {len(links)} release-track links")
        return links

    def _extract_links_for_release(self, release: Dict) -> List[Dict[str, Any]]:
        """Extract track links for a single release"""
        links = []

        # Generate release business key
        release_business_key = self._get_release_business_key(release)

        # Extract track references from resource groups
        resource_groups = self._safe_get(
            release, 'ReleaseDetailsByTerritory.ResourceGroup.ResourceGroup', [])

        for resource_group in self._ensure_list(resource_groups):
            volume = self._safe_get(resource_group, 'SequenceNumber')
            content_items = self._safe_get(
                resource_group, 'ResourceGroupContentItem', [])

            for item in self._ensure_list(content_items):
                sequence = self._safe_get(item, 'SequenceNumber')
                track_link = self._extract_single_track_link(
                    item, release_business_key, sequence, volume
                )
                if track_link:
                    links.append(track_link)

        return links

    def _extract_single_track_link(
        self,
        content_item: Dict,
        release_business_key: str,
        sequence: int,
        volume: int
    ) -> Dict[str, Any]:
        """Extract a single track link from a content item"""

        # Get the resource reference
        resource_ref = self._safe_get(
            content_item, 'ReleaseResourceReference.#text')
        if not resource_ref:
            self.logger.debug("No resource reference found in content item")
            return None

        # Map resource reference to ISRC (track business key)
        track_business_key = self._map_resource_reference_to_isrc(resource_ref)
        if not track_business_key:
            self.logger.warning(
                f"Could not map resource reference {resource_ref} to ISRC")
            return None

        return {
            'release_business_key': release_business_key,
            'track_business_key': track_business_key,
            'sequence': sequence,
            'volume': volume
        }

    def _get_release_business_key(self, release: Dict) -> str:
        """Generate business key for a release"""
        upc = self._safe_get(release, 'ReleaseId.ICPN.#text')
        catalog_number = self._safe_get(
            release, 'ReleaseId.CatalogNumber.#text')
        return f"{upc}|{catalog_number}"

    def _map_resource_reference_to_isrc(self, resource_reference: str) -> str:
        """Map a resource reference to its corresponding ISRC"""
        # Look up the resource in the ResourceList to find its ISRC
        sound_recordings = self._safe_get(
            self.ddex_data, 'ResourceList.SoundRecording', [])

        for recording in self._ensure_list(sound_recordings):
            # Check if this recording matches the resource reference
            recording_ref = self._safe_get(recording, 'ResourceReference')
            if recording_ref == resource_reference:
                isrc = self._safe_get(recording, 'SoundRecordingId.ISRC')
                if isrc:
                    rights_controller_data = self._safe_get(
                        recording, 'SoundRecordingDetailsByTerritory.RightsController')
                    controller = self._ensure_list(rights_controller_data)[0]
                    name = self._safe_get(controller, 'PartyName.FullName')
                    return f"{name}|{isrc}"
                else:
                    self.logger.warning(
                        f"No ISRC found for resource reference {resource_reference}")

        self.logger.debug(
            f"Resource reference {resource_reference} not found in ResourceList")
        return None

    def get_business_key(self, link_data: Dict) -> str:
        """Generate business key for a release-track link"""
        # Combination of both entity business keys plus sequence/volume for uniqueness
        return f"{link_data['release_business_key']}|{link_data['track_business_key']}|{link_data['sequence']}|{link_data['volume']}"
