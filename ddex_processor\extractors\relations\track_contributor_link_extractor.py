# extractors/relations/track_contributor_link_extractor.py

from typing import List, Dict, Any
from ..base_extractor import BaseExtractor


class TrackContributorLinkExtractor(BaseExtractor):
    """Extract track-contributor linking data from DDEX JSON (excludes publishers)"""

    def __init__(self, ddex_data: Dict):
        super().__init__(ddex_data)
        # Define publisher roles to filter out
        self.publisher_roles = {
            'MusicPublisher',
            'Publisher',
            'SubPublisher',
            'AdministrativePublisher',
            'OriginalPublisher',
            'PublisherAdministrator',
            'PublishingCompany'
        }

    def extract(self) -> List[Dict[str, Any]]:
        """Extract all track-contributor links from DDEX data (excluding publishers)"""
        self.logger.debug("Extracting track-contributor links")
        links = []

        sound_recordings = self._safe_get(
            self.ddex_data, 'ResourceList.SoundRecording', [])

        for recording in self._ensure_list(sound_recordings):
            track_links = self._extract_links_for_recording(recording)
            links.extend(track_links)

        self.logger.debug(
            f"Extracted {len(links)} track-contributor links (publishers filtered out)")
        return links

    def _extract_links_for_recording(self, recording: Dict) -> List[Dict[str, Any]]:
        """Extract contributor links for a single track"""
        links = []

        # Generate track business key
        track_business_key = self._get_business_key_from_recording(recording)

        # Get sound recording details (handle both dict and list structures)
        sound_recording_details = self._safe_get(
            recording, 'SoundRecordingDetailsByTerritory', {})
        if isinstance(sound_recording_details, list):
            sound_recording_details = sound_recording_details[0] if sound_recording_details else {
            }

        # Extract indirect resource contributors
        indirect_contributors = self._safe_get(
            sound_recording_details, 'IndirectResourceContributor', []
        )

        for contributor_data in self._ensure_list(indirect_contributors):
            contributor_links = self._extract_contributor_with_multiple_roles(
                contributor_data, track_business_key, 'IndirectResourceContributorRole'
            )
            links.extend(contributor_links)

        # Extract direct resource contributors
        direct_contributors = self._safe_get(
            sound_recording_details, 'ResourceContributor', []
        )

        for contributor_data in self._ensure_list(direct_contributors):
            contributor_links = self._extract_contributor_with_multiple_roles(
                contributor_data, track_business_key, 'ResourceContributorRole'
            )
            links.extend(contributor_links)

        return links

    def _extract_contributor_with_multiple_roles(
        self,
        contributor_data: Dict,
        track_business_key: str,
        role_field_name: str
    ) -> List[Dict[str, Any]]:
        """Extract contributor links handling multiple roles per contributor"""
        links = []

        # Get contributor name
        contributor_name = self._extract_contributor_name(contributor_data)
        if not contributor_name:
            self.logger.debug("No contributor name found in contributor data")
            return links

        # Get all roles for this contributor (could be single role or list of roles)
        roles_data = self._safe_get(contributor_data, role_field_name, [])
        roles = self._ensure_list(roles_data)

        # If no roles specified, use default
        if not roles:
            roles = ['Contributor']

        # Create a link for each role (filtering out publishers)
        for role in roles:
            if not role:
                continue

            # Extract role string from role data (handle both string and dict)
            role_string = self._extract_role_string(role)
            if not role_string:
                continue

            # Skip publisher roles
            if self._is_publisher_role(role_string):
                self.logger.debug(
                    f"Skipping publisher role '{role_string}' for contributor '{contributor_name}'")
                continue

            # Generate business keys
            contributor_business_key = contributor_name
            role_business_key = f"{role_string}|contributor"

            link = {
                'track_business_key': track_business_key,
                'contributor_business_key': contributor_business_key,
                'role_business_key': role_business_key
            }

            links.append(link)

        return links

    def _extract_role_string(self, role_data) -> str:
        """Extract role string from role data (handles both string and dict formats)"""
        if isinstance(role_data, str):
            return role_data

        if isinstance(role_data, dict):
            # Handle UserDefined roles with @UserDefinedValue
            if role_data.get('#text') == 'UserDefined' and '@UserDefinedValue' in role_data:
                return role_data.get('@UserDefinedValue')

            # Try common role field names
            role_candidates = [
                # Add this first for UserDefined roles
                role_data.get('@UserDefinedValue'),
                role_data.get('RoleType'),
                role_data.get('Role'),
                role_data.get('Type'),
                role_data.get('Name'),
                role_data.get('#text')  # Sometimes roles are in text content
            ]

            for candidate in role_candidates:
                if candidate and isinstance(candidate, str):
                    return candidate.strip()

        return None

    def _is_publisher_role(self, role: str) -> bool:
        """Check if a role is a publisher role that should be filtered out"""
        if not role:
            return False

        # Check if the role matches any known publisher roles (case-insensitive)
        return role in self.publisher_roles or role.lower() in {r.lower() for r in self.publisher_roles}

    def _extract_contributor_name(self, contributor_data: Dict) -> str:
        """Extract contributor name from various possible structures"""
        # Try different possible name fields
        name_candidates = [
            self._safe_get(contributor_data, 'PartyName.FullName'),
            self._safe_get(contributor_data, 'ContributorName'),
            self._safe_get(contributor_data, 'PartyName.DisplayName'),
            self._safe_get(contributor_data, 'Name'),
            self._safe_get(contributor_data, 'PartyName.LegalName')
        ]

        # Return the first non-empty name found
        for name in name_candidates:
            if name and isinstance(name, str) and name.strip():
                return name.strip()

        return None

    def get_business_key(self, link_data: Dict) -> str:
        """Generate business key for a track-contributor link"""
        # Combination of all three entity business keys for uniqueness
        return f"{link_data['track_business_key']}|{link_data['contributor_business_key']}|{link_data['role_business_key']}"

    def _get_business_key_from_recording(self, recording: Dict) -> str:
        """Generate business key for a track from a sound recording"""
        rights_controller_data = self._safe_get(
            recording, 'SoundRecordingDetailsByTerritory.RightsController', []
        )

        controller = self._ensure_list(rights_controller_data)[0]
        name = self._safe_get(controller, 'PartyName.FullName')

        return f"{name}|{self._safe_get(recording, 'SoundRecordingId.ISRC')}"

    def get_publisher_roles(self) -> set:
        """Get the set of roles that are considered publisher roles"""
        return self.publisher_roles.copy()

    def add_publisher_role(self, role: str):
        """Add a new role to the publisher filter list"""
        self.publisher_roles.add(role)

    def remove_publisher_role(self, role: str):
        """Remove a role from the publisher filter list"""
        self.publisher_roles.discard(role)
