# processors/core/rights_controller_processor.py

from typing import Dict
from sqlalchemy.orm import Session
import logging

from ddex_processor.models.generated import RightsController
from ddex_processor.extractors.entities.rights_controller_extractor import RightsControllerExtractor


class RightsControllerProcessor:
    """SQLAlchemy-based processor for RightsController entities"""

    def __init__(self, update_existing: bool = False):
        self.update_existing = update_existing
        self.logger = logging.getLogger(self.__class__.__name__)

    def process_rights_controllers(self, session: Session, ddex_data: Dict) -> Dict[str, RightsController]:
        """Process core rights controller entities only (Phase 1)"""
        self.logger.debug("Processing rights controllers...")

        extractor = RightsControllerExtractor(ddex_data)
        rights_controller_data_list = extractor.extract_core_data()

        entity_map = {}

        for rights_controller_data in rights_controller_data_list:
            business_key = extractor.get_business_key(rights_controller_data)
            rights_controller = self._upsert_rights_controller(
                session, rights_controller_data)
            entity_map[business_key] = rights_controller

        self.logger.info(f"Processed {len(entity_map)} rights controllers")
        return entity_map

    def _upsert_rights_controller(self, session: Session, rights_controller_data: Dict) -> RightsController:
        """Upsert a rights controller using party_id + name lookup"""
        # Find existing rights controller by business key (party_id + name)
        existing = session.query(RightsController).filter_by(
            party_id=rights_controller_data['party_id'],
            name=rights_controller_data['name']
        ).first()

        if existing and self.update_existing:
            # Update existing rights controller (though rights controllers rarely change)
            self.logger.debug(
                f"Updating existing rights controller: {existing.name}")
            for key, value in rights_controller_data.items():
                if hasattr(existing, key) and key != 'id':
                    setattr(existing, key, value)
            return existing

        elif existing:
            # Skip update, return existing
            self.logger.debug(
                f"Skipping existing rights controller: {existing.name}")
            return existing

        else:
            # Create new rights controller
            self.logger.debug(
                f"Creating new rights controller: {rights_controller_data['name']}")
            new_rights_controller = RightsController(**rights_controller_data)
            session.add(new_rights_controller)
            return new_rights_controller
