# extractors/entities/role_extractor.py

from typing import List, Dict, Any, Set
from ..base_extractor import BaseExtractor


class RoleExtractor(BaseExtractor):
    """Extract role data from DDEX JSON with SQLAlchemy architecture"""

    def extract(self) -> List[Dict[str, Any]]:
        """DEPRECATED: Use extract_core_data() instead"""
        return self.extract_core_data()

    def extract_core_data(self) -> List[Dict[str, Any]]:
        """Extract CORE role data only (no relationships)"""
        self.logger.debug("Extracting roles")
        roles = set()

        # Extract artist roles from sound recordings
        sound_recordings = self._safe_get(
            self.ddex_data, 'ResourceList.SoundRecording', []
        )
        for recording in self._ensure_list(sound_recordings):
            roles.update(self._extract_artist_roles_from_recording(recording))

        # Extract artist roles from releases
        releases = self._safe_get(self.ddex_data, 'ReleaseList.Release', [])
        for release in self._ensure_list(releases):
            if release.get('@IsMainRelease') == 'true':
                roles.update(self._extract_artist_roles_from_release(release))

        # Extract contributor roles from recordings
        for recording in self._ensure_list(sound_recordings):
            roles.update(
                self._extract_contributor_roles_from_recording(recording))

        # Add some common default roles that might be missing
        roles.update(self._get_default_roles())

        # Convert set of tuples to list of dictionaries
        role_list = [
            {
                'name': role_name,
                'type': role_type,
            }
            for role_type, role_name in roles
        ]

        self.logger.debug(f"Extracted {len(role_list)} unique roles")
        return role_list

    def extract_metadata(self) -> Dict[str, List[Dict[str, Any]]]:
        """Extract relationship/metadata separately for Phase 2 processing"""
        # Roles don't have metadata relationships in this system
        # Return empty dict to maintain consistency with the pattern
        return {
            'permissions': [],      # Could add if you have role permissions
            'hierarchies': []       # Could add if you have role hierarchies
        }

    def _extract_role_string(self, role_data) -> str:
        """Extract role string from role data (handles both string and dict formats)"""
        if isinstance(role_data, str):
            return role_data

        if isinstance(role_data, dict):
            # Handle UserDefined roles with @UserDefinedValue or UserDefinedValue
            if role_data.get('#text') == 'UserDefined':
                user_defined = role_data.get(
                    '@UserDefinedValue') or role_data.get('UserDefinedValue')
                if user_defined:
                    return user_defined

            # Try common role field names
            role_candidates = [
                role_data.get('@UserDefinedValue'),
                role_data.get('UserDefinedValue'),
                role_data.get('RoleType'),
                role_data.get('Role'),
                role_data.get('Type'),
                role_data.get('Name'),
                role_data.get('#text')  # Sometimes roles are in text content
            ]

            for candidate in role_candidates:
                if candidate and isinstance(candidate, str):
                    return candidate.strip()

        return None

    def _extract_artist_roles_from_recording(self, recording: Dict) -> Set[tuple]:
        """Extract artist roles from sound recording"""
        roles = set()

        # Get recording details (handle both dict and list structures)
        recording_details = self._safe_get(
            recording, 'SoundRecordingDetailsByTerritory', {}
        )
        if isinstance(recording_details, list):
            recording_details = recording_details[0] if recording_details else {
            }

        # Extract display artist roles
        display_artists = self._safe_get(
            recording_details, 'DisplayArtist', [])
        for artist in self._ensure_list(display_artists):
            roles_data = self._safe_get(artist, 'ArtistRole')

            # Handle multiple roles per artist
            if not roles_data:
                # Default role if none specified
                roles.add(('artist', 'MainArtist'))
            elif isinstance(roles_data, list):
                # Multiple roles as list
                for role_item in roles_data:
                    role_string = self._extract_role_string(role_item)
                    if role_string:
                        roles.add(('artist', role_string))
            else:
                # Single role (string or dict)
                role_string = self._extract_role_string(roles_data)
                if role_string:
                    roles.add(('artist', role_string))
                else:
                    # Default role if we can't extract the role
                    roles.add(('artist', 'MainArtist'))

        return roles

    def _extract_artist_roles_from_release(self, release: Dict) -> Set[tuple]:
        """Extract artist roles from release"""
        roles = set()

        # Get release details (handle both dict and list structures)
        release_details = self._safe_get(
            release, 'ReleaseDetailsByTerritory', {}
        )
        if isinstance(release_details, list):
            release_details = release_details[0] if release_details else {}

        # Extract display artist roles
        display_artists = self._safe_get(release_details, 'DisplayArtist', [])
        for artist in self._ensure_list(display_artists):
            roles_data = self._safe_get(artist, 'ArtistRole')

            # Handle multiple roles per artist
            if not roles_data:
                # Default role if none specified
                roles.add(('artist', 'MainArtist'))
            elif isinstance(roles_data, list):
                # Multiple roles as list
                for role_item in roles_data:
                    role_string = self._extract_role_string(role_item)
                    if role_string:
                        roles.add(('artist', role_string))
            else:
                # Single role (string or dict)
                role_string = self._extract_role_string(roles_data)
                if role_string:
                    roles.add(('artist', role_string))
                else:
                    # Default role if we can't extract the role
                    roles.add(('artist', 'MainArtist'))

        return roles

    def _extract_contributor_roles_from_recording(self, recording: Dict) -> Set[tuple]:
        """Extract contributor roles from sound recording"""
        roles = set()

        # Get recording details
        recording_details = self._safe_get(
            recording, 'SoundRecordingDetailsByTerritory', {}
        )
        if isinstance(recording_details, list):
            recording_details = recording_details[0] if recording_details else {
            }

        # Extract indirect resource contributors
        contributors = self._safe_get(
            recording_details, 'IndirectResourceContributor', []
        )

        for contributor in self._ensure_list(contributors):
            contrib_roles = self._safe_get(
                contributor, 'IndirectResourceContributorRole', []
            )

            for role_data in self._ensure_list(contrib_roles):
                if role_data:
                    role_string = self._extract_role_string(role_data)
                    if role_string:
                        roles.add(('contributor', role_string))

        # Extract direct resource contributors
        direct_contributors = self._safe_get(
            recording_details, 'ResourceContributor', []
        )

        for contributor in self._ensure_list(direct_contributors):
            contrib_roles = self._safe_get(
                contributor, 'ResourceContributorRole', []
            )

            for role_data in self._ensure_list(contrib_roles):
                if role_data:
                    role_string = self._extract_role_string(role_data)
                    if role_string:
                        roles.add(('contributor', role_string))

        # Also extract from general Contributors field (some DDEX files use this)
        general_contributors = self._safe_get(
            recording_details, 'Contributor', [])
        for contributor in self._ensure_list(general_contributors):
            role_data = self._safe_get(contributor, 'ContributorRole')
            if role_data:
                role_string = self._extract_role_string(role_data)
                if role_string:
                    roles.add(('contributor', role_string))

        return roles

    def _get_default_roles(self) -> Set[tuple]:
        """Get common default roles that should always exist"""
        return {
            ('artist', 'MainArtist'),
            ('artist', 'FeaturedArtist'),
            ('contributor', 'Composer'),
            ('contributor', 'Lyricist'),
            ('contributor', 'Producer'),
            ('contributor', 'MusicPublisher'),
            ('contributor', 'AssociatedPerformer'),
            ('contributor', 'Mixer'),  # Add common UserDefined roles
            ('contributor', 'Contributor')
        }

    def get_business_key(self, entity_data: Dict) -> str:
        """Generate business key for role"""
        return f"{entity_data['name']}|{entity_data['type']}"
