# processors/relations/track_publisher_link_processor.py

from typing import Dict, List
from sqlalchemy.orm import Session
import logging

from ddex_processor.models.generated import TrackPublisherLinks
from ddex_processor.extractors.relations.track_publisher_link_extractor import TrackPublisherLinkExtractor


class TrackPublisherLinkProcessor:
    """Processor for track-publisher many-to-many relationships"""

    def __init__(self, update_existing: bool = False):
        self.update_existing = update_existing
        self.logger = logging.getLogger(self.__class__.__name__)

    def process_track_publisher_links(
        self,
        session: Session,
        ddex_data: Dict,
        entity_maps: Dict
    ) -> bool:
        """Process all track-publisher links"""
        self.logger.debug("Processing track-publisher links...")

        try:
            # Extract link data
            extractor = TrackPublisherLinkExtractor(ddex_data)
            link_data_list = extractor.extract()

            if not link_data_list:
                self.logger.debug("No track-publisher links found")
                return True

            # Process each link
            processed_count = 0
            skipped_count = 0

            for link_data in link_data_list:
                if self.validate_link_data(link_data):
                    if self._process_single_link(session, link_data, entity_maps):
                        processed_count += 1
                    else:
                        skipped_count += 1
                else:
                    skipped_count += 1

            self.logger.info(
                f"Processed {processed_count} track-publisher links, "
                f"skipped {skipped_count} due to missing entities or validation errors"
            )

            return True

        except Exception as e:
            self.logger.error(
                f"Error processing track-publisher links: {str(e)}")
            return False

    def _process_single_link(
        self,
        session: Session,
        link_data: Dict,
        entity_maps: Dict
    ) -> bool:
        """Process a single track-publisher link"""

        # Look up the entities
        track = entity_maps.get('tracks', {}).get(
            link_data['track_business_key'])
        publisher = entity_maps.get('publishers', {}).get(
            link_data['publisher_business_key'])
        role = entity_maps.get('roles', {}).get(link_data['role_business_key'])

        # Validate that all entities exist
        missing_entities = []

        if not track:
            missing_entities.append(
                f"track: {link_data['track_business_key']}")

        if not publisher:
            missing_entities.append(
                f"publisher: {link_data['publisher_business_key']}")

        if not role:
            missing_entities.append(f"role: {link_data['role_business_key']}")

        if missing_entities:
            self.logger.warning(
                f"Skipping track-publisher link due to missing entities: {', '.join(missing_entities)}"
            )
            return False

        # Create or update the link
        return self._upsert_link(session, track, publisher, role, link_data)

    def _upsert_link(
        self,
        session: Session,
        track,
        publisher,
        role,
        link_data: Dict
    ) -> bool:
        """Create or update a track-publisher link"""
        try:
            # Check if link already exists
            existing_link = session.query(TrackPublisherLinks).filter_by(
                track_id=track.id,
                publisher_id=publisher.id,
                role_id=role.id
            ).first()

            if existing_link:
                if self.update_existing:
                    # For this table, there's not much to update beyond the three IDs
                    # Could add fields like share_percentage, territory, etc. in the future
                    self.logger.debug(
                        f"Link already exists: Track {track.id} -> Publisher {publisher.id} "
                        f"(Role: {role.id})"
                    )
                    return True
                else:
                    # Skip existing link
                    self.logger.debug(
                        f"Skipping existing link: Track {track.id} -> Publisher {publisher.id}"
                    )
                    return True
            else:
                # Create new link
                new_link = TrackPublisherLinks(
                    track_id=track.id,
                    publisher_id=publisher.id,
                    role_id=role.id
                )

                session.add(new_link)

                self.logger.debug(
                    f"Created link: Track {track.id} -> Publisher {publisher.id} "
                    f"(Role: {role.name})"
                )
                return True

        except Exception as e:
            self.logger.error(
                f"Error creating track-publisher link: {str(e)}"
            )
            return False

    def clear_track_publisher_links(self, session: Session, track_id: str):
        """Clear all publisher links for a specific track (useful for full replacement)"""
        try:
            deleted_count = session.query(TrackPublisherLinks).filter_by(
                track_id=track_id
            ).delete()

            self.logger.info(
                f"Cleared {deleted_count} publisher links for track {track_id}")

        except Exception as e:
            self.logger.error(
                f"Error clearing track publisher links: {str(e)}")

    def get_links_for_track(self, session: Session, track_id: str) -> List[TrackPublisherLinks]:
        """Get all publisher links for a specific track"""
        try:
            links = session.query(TrackPublisherLinks).filter_by(
                track_id=track_id
            ).all()

            return links

        except Exception as e:
            self.logger.error(f"Error getting track publisher links: {str(e)}")
            return []

    def get_links_for_publisher(self, session: Session, publisher_id: str) -> List[TrackPublisherLinks]:
        """Get all track links for a specific publisher"""
        try:
            links = session.query(TrackPublisherLinks).filter_by(
                publisher_id=publisher_id
            ).all()

            return links

        except Exception as e:
            self.logger.error(f"Error getting publisher track links: {str(e)}")
            return []

    def get_publishers_for_track(self, session: Session, track_id: str, role_name: str = None) -> List[Dict]:
        """Get all publishers for a track, optionally filtered by role"""
        try:
            from models.generated import Publisher, Role

            query = session.query(
                TrackPublisherLinks, Publisher, Role
            ).join(
                Publisher, TrackPublisherLinks.publisher_id == Publisher.id
            ).join(
                Role, TrackPublisherLinks.role_id == Role.id
            ).filter(
                TrackPublisherLinks.track_id == track_id
            )

            if role_name:
                query = query.filter(Role.name == role_name)

            results = query.all()

            return [
                {
                    'publisher': publisher,
                    'role': role,
                    'link': link
                }
                for link, publisher, role in results
            ]

        except Exception as e:
            self.logger.error(f"Error getting publishers for track: {str(e)}")
            return []

    def get_tracks_for_publisher(self, session: Session, publisher_id: str, role_name: str = None) -> List[Dict]:
        """Get all tracks for a publisher, optionally filtered by role"""
        try:
            from models.generated import Track, Role

            query = session.query(
                TrackPublisherLinks, Track, Role
            ).join(
                Track, TrackPublisherLinks.track_id == Track.id
            ).join(
                Role, TrackPublisherLinks.role_id == Role.id
            ).filter(
                TrackPublisherLinks.publisher_id == publisher_id
            )

            if role_name:
                query = query.filter(Role.name == role_name)

            results = query.all()

            return [
                {
                    'track': track,
                    'role': role,
                    'link': link
                }
                for link, track, role in results
            ]

        except Exception as e:
            self.logger.error(f"Error getting tracks for publisher: {str(e)}")
            return []

    def validate_link_data(self, link_data: Dict) -> bool:
        """Validate that link data has all required fields"""
        required_fields = ['track_business_key',
                           'publisher_business_key', 'role_business_key']

        for field in required_fields:
            if field not in link_data or not link_data[field]:
                self.logger.warning(
                    f"Missing required field '{field}' in link data")
                return False

        return True

    def get_publisher_statistics(self, session: Session) -> Dict[str, int]:
        """Get statistics about track-publisher relationships"""
        try:
            total_links = session.query(TrackPublisherLinks).count()

            unique_tracks = session.query(
                TrackPublisherLinks.track_id).distinct().count()
            unique_publishers = session.query(
                TrackPublisherLinks.publisher_id).distinct().count()

            return {
                'total_links': total_links,
                'unique_tracks_with_publishers': unique_tracks,
                'unique_publishers': unique_publishers
            }

        except Exception as e:
            self.logger.error(f"Error getting publisher statistics: {str(e)}")
            return {}
