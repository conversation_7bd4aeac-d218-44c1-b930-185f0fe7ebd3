from typing import List, Dict, Any
from ..base_extractor import MultiEntityExtractor


class DealExtractor(MultiEntityExtractor):
    """Extract deal data from DDEX JSON with SQLAlchemy architecture"""

    def extract(self) -> List[Dict[str, Any]]:
        """DEPRECATED: Use extract_core_data() instead"""
        return self.extract_core_data()

    def extract_core_data(self) -> Dict[str, List[Dict[str, Any]]]:
        """Extract CORE deal entities only (Phase 1) - DealGroup and Deal"""
        deals_data = self._safe_get(self.ddex_data, 'DealList.ReleaseDeal', [])

        deal_groups = []
        deals = []

        for deal_data in self._ensure_list(deals_data):
            # Extract DealGroup data
            deal_group_data = self._extract_core_deal_group_data(deal_data)
            if deal_group_data:
                deal_groups.append(deal_group_data)

                # Extract Deal data within this DealGroup
                deal_terms = self._safe_get(deal_data, 'Deal', [])
                for deal_term in self._ensure_list(deal_terms):
                    deal_data_item = self._extract_core_deal_data(
                        deal_term, deal_group_data)
                    if deal_data_item:
                        deals.append(deal_data_item)

        return {
            'deal_groups': deal_groups,
            'deals': deals
        }

    def extract_metadata(self) -> Dict[str, List[Dict[str, Any]]]:
        """Extract relationship/metadata separately for Phase 2 processing"""
        deals_data = self._safe_get(self.ddex_data, 'DealList.ReleaseDeal', [])

        pricing_data = []
        territories_data = []
        usage_types_data = []

        for deal_data in self._ensure_list(deals_data):
            deal_group_key = self._get_deal_group_business_key_from_deal(
                deal_data)

            deal_terms = self._safe_get(deal_data, 'Deal', [])
            for deal_term in self._ensure_list(deal_terms):
                deal_key = self._get_deal_business_key_from_term(
                    deal_term, deal_group_key)

                # Extract pricing data
                price_info = self._safe_get(
                    deal_term, 'DealTerms.PriceInformation')
                if price_info:
                    pricing_item = self._extract_pricing_data(
                        price_info, deal_key)
                    if pricing_item:
                        pricing_data.append(pricing_item)

                # Extract territory data - handle both included and excluded territories
                excluded_territories = self._safe_get(
                    deal_term, 'DealTerms.ExcludedTerritoryCode', [])
                for territory in self._ensure_list(excluded_territories):
                    territory_item = self._extract_territory_data(
                        territory, deal_key, is_excluded=True)
                    if territory_item:
                        territories_data.append(territory_item)

                included_territories = self._safe_get(
                    deal_term, 'DealTerms.TerritoryCode', [])
                for territory in self._ensure_list(included_territories):
                    territory_item = self._extract_territory_data(
                        territory, deal_key, is_excluded=False)
                    if territory_item:
                        territories_data.append(territory_item)

                usage_info = self._safe_get(deal_term, 'DealTerms.Usage')
                if usage_info:
                    use_type = self._safe_get(usage_info, 'UseType')
                    if use_type:
                        usage_item = self._extract_usage_type_data(
                            use_type, deal_key)
                        if usage_item:
                            usage_types_data.append(usage_item)

        return {
            'pricing': pricing_data,
            'territories': territories_data,
            'usage_types': usage_types_data
        }

    def _extract_core_deal_group_data(self, deal_data: Dict) -> Dict[str, Any]:
        """Extract ONLY core deal group fields (no relationships)"""
        deal_release_reference = self._safe_get(
            deal_data, 'DealReleaseReference')
        effective_date = self._safe_get(deal_data, 'EffectiveDate')

        if not deal_release_reference:
            return None

        return {
            'deal_release_reference': deal_release_reference,
            'effective_date': effective_date,
        }

    def _extract_core_deal_data(self, deal_term: Dict, deal_group_data: Dict) -> Dict[str, Any]:
        """Extract ONLY core deal fields (no relationships)"""
        commercial_model_type = self._safe_get(
            deal_term, 'DealTerms.CommercialModelType')
        start_date = self._safe_get(
            deal_term, 'DealTerms.ValidityPeriod.StartDate')
        end_date = self._safe_get(
            deal_term, 'DealTerms.ValidityPeriod.EndDate')

        if not commercial_model_type or not start_date:
            return None

        return {
            'commercial_model_type': commercial_model_type,
            'start_date': start_date,
            'end_date': end_date,
            'deal_group_business_key': self.get_deal_group_business_key(deal_group_data),
        }

    def _extract_pricing_data(self, price: Dict, deal_business_key: str) -> Dict[str, Any]:
        """Extract pricing metadata"""
        price_range_type = self._safe_get(price, 'PriceRangeType.#text')
        price_type = self._safe_get(price, 'PriceType.#text')

        return {
            'deal_business_key': deal_business_key,
            'price_range_type': price_range_type,
            'price_type': price_type,
        }

    def _extract_territory_data(self, territory: Dict, deal_business_key: str, is_excluded: bool = False) -> Dict[str, Any]:
        """Extract territory metadata"""
        if isinstance(territory, str):
            territory_code = territory
        else:
            territory_code = self._safe_get(
                territory, '#text') or str(territory)

        return {
            'deal_business_key': deal_business_key,
            'territory_code': territory_code,
            'is_excluded': is_excluded
        }

    def _extract_usage_type_data(self, usage_type: Dict, deal_business_key: str) -> Dict[str, Any]:
        """Extract usage type metadata"""
        usage_type_value = usage_type if isinstance(
            usage_type, str) else self._safe_get(usage_type, '#text', str(usage_type))

        return {
            'deal_business_key': deal_business_key,
            'usage_type': usage_type_value
        }

    def _get_deal_group_business_key_from_deal(self, deal_data: Dict) -> str:
        """Generate deal group business key from deal dict"""
        deal_release_reference = self._safe_get(
            deal_data, 'DealReleaseReference')
        effective_date = self._safe_get(deal_data, 'EffectiveDate')
        return f"{deal_release_reference}|{effective_date}"

    def _get_deal_business_key_from_term(self, deal_term: Dict, deal_group_key: str) -> str:
        """Generate deal business key from deal term dict"""
        commercial_model_type = self._safe_get(
            deal_term, 'DealTerms.CommercialModelType')
        start_date = self._safe_get(
            deal_term, 'DealTerms.ValidityPeriod.StartDate')
        end_date = self._safe_get(
            deal_term, 'DealTerms.ValidityPeriod.EndDate')
        return f"{deal_group_key}|{commercial_model_type}|{start_date}|{end_date}"

    def get_deal_group_business_key(self, deal_group_data: Dict) -> str:
        """Generate deal group business key from entity data dict"""
        return f"{deal_group_data['deal_release_reference']}|{deal_group_data['effective_date']}"

    def get_deal_business_key(self, deal_data: Dict) -> str:
        """Generate deal business key from entity data dict"""
        return f"{deal_data['deal_group_business_key']}|{deal_data['commercial_model_type']}|{deal_data['start_date']}|{deal_data['end_date']}"

    def get_release_business_key_for_deal_group(self, deal_group_data: Dict) -> str:
        """Extract release business key that this deal group should link to"""
        deal_release_reference = deal_group_data['deal_release_reference']

        # Look up the actual release using the reference - MAIN RELEASES ONLY
        releases = self._safe_get(self.ddex_data, 'ReleaseList.Release', [])
        for release in self._ensure_list(releases):
            release_reference = self._safe_get(release, 'ReleaseReference')
            is_main_release = self._safe_get(
                release, '@IsMainRelease') == 'true'

            if release_reference == deal_release_reference and is_main_release:
                # Found the matching MAIN release, extract its business key components
                upc = self._safe_get(release, 'ReleaseId.ICPN.#text')
                catalog_number = self._safe_get(
                    release, 'ReleaseId.CatalogNumber.#text')
                return f"{upc}|{catalog_number}"

        # If no matching MAIN release found, this might be a track release - skip it
        return None  # Signal to processor to skip this deal group
