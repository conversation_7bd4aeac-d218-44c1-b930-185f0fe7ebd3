# processors/core/track_processor.py

from typing import Dict, List
from sqlalchemy.orm import Session
import logging

# Import SQLAlchemy models (from generated file)
from ddex_processor.models.generated import Track, TrackFuga, TrackAlternateTitles
from ddex_processor.extractors.entities.track_extractor import TrackExtractor
from ddex_processor.db.batch_operations import BatchProcessor


class TrackProcessor:
    """SQLAlchemy-based processor for Track entities and their metadata"""

    def __init__(self, update_existing: bool = False):
        self.update_existing = update_existing
        self.logger = logging.getLogger(self.__class__.__name__)

    def process_tracks(self, session: Session, ddex_data: Dict) -> Dict[str, Track]:
        """Process core track entities only (Phase 1)"""
        self.logger.debug("Processing tracks...")

        extractor = TrackExtractor(ddex_data)
        track_data_list = extractor.extract_core_data()

        # Always use batch processing for better performance
        return self._process_tracks_batch(session, track_data_list, extractor)

    def _process_tracks_batch(self, session: Session, track_data_list: List[Dict], extractor: TrackExtractor) -> Dict[str, Track]:
        """Process tracks using batch operations for better performance"""
        if not track_data_list:
            self.logger.info("Processed 0 tracks")
            return {}

        batch_processor = BatchProcessor(session)

        # Perform bulk upsert using ISRC + catalogue as conflict columns (composite unique constraint)
        results = batch_processor.bulk_upsert(
            model_class=Track,
            data_list=track_data_list,
            # Tracks are unique by ISRC + catalogue
            conflict_columns=['isrc', 'catalogue'],
            # Update these fields on conflict
            update_columns=['title', 'duration_seconds',
                            'genre', 'language_of_performance']
        )

        # Build entity map using business keys
        entity_map = {}
        for i, track_data in enumerate(track_data_list):
            business_key = extractor.get_business_key(track_data)
            if i < len(results):
                # Create Track object from result row
                track = Track()
                for column in Track.__table__.columns:
                    if hasattr(results[i], column.name):
                        setattr(track, column.name, getattr(
                            results[i], column.name))
                entity_map[business_key] = track

        self.logger.info(f"Processed {len(entity_map)} tracks")
        return entity_map

    def process_track_metadata(self, session: Session, ddex_data: Dict, track_map: Dict[str, Track]):
        """Process track metadata relationships (Phase 2)"""
        self.logger.debug("Processing track metadata...")

        extractor = TrackExtractor(ddex_data)
        metadata = extractor.extract_metadata()

        # Process Fuga relationships
        fuga_count = 0
        for fuga_data in metadata.get('fuga', []):
            track_key = fuga_data['track_business_key']
            if track_key in track_map:
                track = track_map[track_key]
                self._upsert_track_fuga(session, track, fuga_data)
                fuga_count += 1

        # Process alternate titles
        title_count = 0
        for title_data in metadata.get('alternate_titles', []):
            track_key = title_data['track_business_key']
            if track_key in track_map:
                track = track_map[track_key]
                self._upsert_track_alternate_title(session, track, title_data)
                title_count += 1

        self.logger.info(
            f"Processed {fuga_count} fuga records and {title_count} alternate titles")

    def _upsert_track(self, session: Session, track_data: Dict) -> Track:
        """Upsert a track using ISRC lookup"""
        # Find existing track by business key (ISRC)
        existing = session.query(Track).filter_by(
            isrc=track_data['isrc']
        ).first()

        if existing and self.update_existing:
            # Update existing track
            self.logger.debug(f"Updating existing track: {existing.title}")
            for key, value in track_data.items():
                if hasattr(existing, key) and key != 'id':
                    setattr(existing, key, value)
            return existing

        elif existing:
            # Skip update, return existing
            self.logger.debug(f"Skipping existing track: {existing.title}")
            return existing

        else:
            # Create new track
            self.logger.debug(
                f"Creating new track: {track_data.get('title', 'Unknown')}")
            new_track = Track(**track_data)
            session.add(new_track)
            return new_track

    def _upsert_track_fuga(self, session: Session, track: Track, fuga_data: Dict):
        """Upsert track fuga metadata with explicit foreign key"""
        existing = session.query(TrackFuga).filter_by(
            track_id=track.id
        ).first()

        if existing:
            # Update existing fuga record
            existing.fuga_id = fuga_data['fuga_id']
            self.logger.debug(f"Updated fuga for track {track.id}")
        else:
            # Create new fuga record
            new_fuga = TrackFuga(
                track_id=track.id,  # Explicit foreign key!
                fuga_id=fuga_data['fuga_id']
            )
            session.add(new_fuga)
            self.logger.debug(f"Created fuga for track {track.id}")

    def _upsert_track_alternate_title(self, session: Session, track: Track, title_data: Dict):
        """Upsert track alternate title with explicit foreign key"""
        # Check if this specific title already exists
        existing = session.query(TrackAlternateTitles).filter_by(
            track_id=track.id,
            title=title_data['title'],
            title_type=title_data['title_type']
        ).first()

        if not existing:
            # Create new alternate title
            new_title = TrackAlternateTitles(
                track_id=track.id,  # Explicit foreign key!
                title=title_data['title'],
                title_type=title_data['title_type']
            )
            session.add(new_title)
            self.logger.debug(
                f"Created alternate title for track {track.id}: {title_data['title']}")

    def clear_track_alternate_titles(self, session: Session, track: Track):
        """Clear all alternate titles for a track (useful for full replacement)"""
        session.query(TrackAlternateTitles).filter_by(
            track_id=track.id
        ).delete()
        self.logger.debug(f"Cleared all alternate titles for track {track.id}")

    def replace_track_alternate_titles(self, session: Session, track: Track, titles_data: List[Dict]):
        """Replace all alternate titles for a track"""
        # Clear existing titles
        self.clear_track_alternate_titles(session, track)

        # Add new titles
        for title_data in titles_data:
            self._upsert_track_alternate_title(session, track, title_data)

        self.logger.debug(
            f"Replaced alternate titles for track {track.id} with {len(titles_data)} new titles")
