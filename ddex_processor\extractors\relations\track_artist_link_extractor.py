# extractors/relations/track_artist_link_extractor.py

from typing import List, Dict, Any
from ..base_extractor import BaseExtractor


class TrackArtistLinkExtractor(BaseExtractor):
    """Extract track-artist linking data from DDEX JSON"""

    def extract(self) -> List[Dict[str, Any]]:
        """Extract all track-artist links from DDEX data"""
        self.logger.info("Extracting track-artist links")
        links = []

        sound_recordings = self._safe_get(
            self.ddex_data, 'ResourceList.SoundRecording', [])

        for recording in self._ensure_list(sound_recordings):
            track_links = self._extract_links_for_recording(recording)
            links.extend(track_links)

        self.logger.info(f"Extracted {len(links)} track-artist links")
        return links

    def _extract_links_for_recording(self, recording: Dict) -> List[Dict[str, Any]]:
        """Extract artist links for a single track"""
        links = []

        # Generate track business key
        track_business_key = self._get_business_key_from_recording(recording)

        # Get sound recording details (handle both dict and list structures)
        sound_recording_details = self._safe_get(
            recording, 'SoundRecordingDetailsByTerritory', {})
        if isinstance(sound_recording_details, list):
            sound_recording_details = sound_recording_details[0] if sound_recording_details else {
            }

        # Extract display artists
        display_artists = self._safe_get(
            sound_recording_details, 'DisplayArtist', [])
        for artist_data in self._ensure_list(display_artists):
            artist_links = self._extract_artist_with_multiple_roles(
                artist_data, track_business_key
            )
            links.extend(artist_links)

        return links

    def _extract_artist_with_multiple_roles(
        self,
        artist_data: Dict,
        track_business_key: str
    ) -> List[Dict[str, Any]]:
        """Extract artist links handling multiple roles per artist"""
        links = []

        # Get artist name
        artist_name = self._extract_artist_name(artist_data)
        if not artist_name:
            self.logger.debug("No artist name found in artist data")
            return links

        # Get artist roles (could be single role, list of roles, or dict)
        roles_data = self._safe_get(artist_data, 'ArtistRole')

        # Handle different role data formats
        if not roles_data:
            # Default to MainArtist if no role specified
            roles = ['MainArtist']
        elif isinstance(roles_data, list):
            # Multiple roles as list
            roles = []
            for role_item in roles_data:
                role_string = self._extract_role_string(role_item)
                if role_string:
                    roles.append(role_string)
        else:
            # Single role (string or dict)
            role_string = self._extract_role_string(roles_data)
            roles = [role_string] if role_string else ['MainArtist']

        # Create a link for each role
        for role in roles:
            if not role:
                continue

            # Generate business keys
            artist_business_key = artist_name
            role_business_key = f"{role}|artist"

            link = {
                'track_business_key': track_business_key,
                'artist_business_key': artist_business_key,
                'role_business_key': role_business_key
            }

            links.append(link)
            self.logger.debug(
                f"Created artist link: {track_business_key} -> {artist_name} ({role})")

        return links

    def _extract_role_string(self, role_data) -> str:
        """Extract role string from role data (handles both string and dict formats)"""
        if isinstance(role_data, str):
            return role_data

        if isinstance(role_data, dict):
            # Handle UserDefined roles with @UserDefinedValue
            if role_data.get('#text') == 'UserDefined':
                user_defined = role_data.get(
                    '@UserDefinedValue') or role_data.get('UserDefinedValue')
                if user_defined:
                    return user_defined

            # Try common role field names
            role_candidates = [
                role_data.get('@UserDefinedValue'),
                role_data.get('UserDefinedValue'),
                role_data.get('RoleType'),
                role_data.get('Role'),
                role_data.get('Type'),
                role_data.get('Name'),
                role_data.get('#text')  # Sometimes roles are in text content
            ]

            for candidate in role_candidates:
                if candidate and isinstance(candidate, str):
                    return candidate.strip()

        return None

    def _extract_artist_name(self, artist_data: Dict) -> str:
        """Extract artist name from various possible structures"""
        # Try different possible name fields
        name_candidates = [
            self._safe_get(artist_data, 'PartyName.FullName'),
            self._safe_get(artist_data, 'ArtistName'),
            self._safe_get(artist_data, 'ContributorName'),
            self._safe_get(artist_data, 'PartyName.DisplayName'),
            self._safe_get(artist_data, 'Name')
        ]

        # Return the first non-empty name found
        for name in name_candidates:
            if name and isinstance(name, str) and name.strip():
                return name.strip()

        return None

    def _get_business_key_from_recording(self, recording: Dict) -> str:
        """Generate business key for a track from a sound recording"""
        rights_controller_data = self._safe_get(
            recording, 'SoundRecordingDetailsByTerritory.RightsController', []
        )

        controller = self._ensure_list(rights_controller_data)[0]
        name = self._safe_get(controller, 'PartyName.FullName')

        return f"{name}|{self._safe_get(recording, 'SoundRecordingId.ISRC')}"

    def get_business_key(self, link_data: Dict) -> str:
        """Generate business key for a track-artist link"""
        # Combination of all three entity business keys for uniqueness
        return f"{link_data['track_business_key']}|{link_data['artist_business_key']}|{link_data['role_business_key']}"
