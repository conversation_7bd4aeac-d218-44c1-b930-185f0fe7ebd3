FROM python:3.12.1-slim

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash ddex

# Set work directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY . .

# Install the package
RUN pip install --no-cache-dir -e .

# Change ownership to non-root user
RUN chown -R ddex:ddex /app

# Switch to non-root user
USER ddex

# Perfect entry point for Batch
ENTRYPOINT ["ddex-processor"]