# processors/relations/track_artist_link_processor.py

from typing import Dict
from sqlalchemy.orm import Session
import logging

from ddex_processor.models.generated import TrackArtistLinks
from ddex_processor.extractors.relations.track_artist_link_extractor import TrackArtistLinkExtractor


class TrackArtistLinkProcessor:
    """Processor for track-artist many-to-many relationships"""

    def __init__(self, update_existing: bool = False):
        self.update_existing = update_existing
        self.logger = logging.getLogger(self.__class__.__name__)

    def process_track_artist_links(
        self,
        session: Session,
        ddex_data: Dict,
        entity_maps: Dict
    ) -> bool:
        """Process all track-artist links"""
        self.logger.debug("Processing track-artist links...")

        try:
            # Extract link data
            extractor = TrackArtistLinkExtractor(ddex_data)
            link_data_list = extractor.extract()

            if not link_data_list:
                self.logger.debug("No track-artist links found")
                return True

            # Process each link
            processed_count = 0
            skipped_count = 0

            for link_data in link_data_list:
                if self._process_single_link(session, link_data, entity_maps):
                    processed_count += 1
                else:
                    skipped_count += 1

            self.logger.info(
                f"Processed {processed_count} track-artist links, "
                f"skipped {skipped_count} due to missing entities"
            )

            return True

        except Exception as e:
            self.logger.error(f"Error processing track-artist links: {str(e)}")
            return False

    def _process_single_link(
        self,
        session: Session,
        link_data: Dict,
        entity_maps: Dict
    ) -> bool:
        """Process a single track-artist link"""

        # Look up the entities
        track = entity_maps.get('tracks', {}).get(
            link_data['track_business_key'])
        artist = entity_maps.get('artists', {}).get(
            link_data['artist_business_key'])
        role = entity_maps.get('roles', {}).get(link_data['role_business_key'])

        # Validate that all entities exist
        missing_entities = []

        if not track:
            missing_entities.append(
                f"track: {link_data['track_business_key']}")

        if not artist:
            missing_entities.append(
                f"artist: {link_data['artist_business_key']}")

        if not role:
            missing_entities.append(f"role: {link_data['role_business_key']}")

        if missing_entities:
            self.logger.warning(
                f"Skipping track-artist link due to missing entities: {', '.join(missing_entities)}"
            )
            return False

        # Create or update the link
        return self._upsert_link(session, track, artist, role, link_data)

    def _upsert_link(
        self,
        session: Session,
        track,
        artist,
        role,
        link_data: Dict
    ) -> bool:
        """Create or update a track-artist link"""
        try:
            # Check if link already exists
            existing_link = session.query(TrackArtistLinks).filter_by(
                track_id=track.id,
                artist_id=artist.id,
                role_id=role.id
            ).first()

            if existing_link:
                if self.update_existing:
                    # For this table, there's not much to update beyond the three IDs
                    self.logger.debug(
                        f"Link already exists: Track {track.id} -> Artist {artist.id} "
                        f"(Role: {role.id})"
                    )
                    return True
                else:
                    # Skip existing link
                    self.logger.debug(
                        f"Skipping existing link: Track {track.id} -> Artist {artist.id}"
                    )
                    return True
            else:
                # Create new link
                new_link = TrackArtistLinks(
                    track_id=track.id,
                    artist_id=artist.id,
                    role_id=role.id
                )

                session.add(new_link)

                self.logger.debug(
                    f"Created link: Track {track.id} -> Artist {artist.id} "
                    f"(Role: {role.name})"
                )
                return True

        except Exception as e:
            self.logger.error(
                f"Error creating track-artist link: {str(e)}"
            )
            return False
