#!/usr/bin/env python3
"""
DDEX Processor - Batch process DDEX files from S3
"""

import logging
import os
from datetime import datetime
from ddex_processor.config.settings import ProcessingConfig
from ddex_processor.processors.s3 import S3Processor
from ddex_processor.db.schema_manager import SchemaManager

# Configuration - Edit these variables as needed
S3_BUCKET = "fuga-data-in"
# Folder prefix to search in
S3_PREFIX = "orbital/"
FILE_FILTER = ".xml"   # Only process files ending with this
FILE_LIMIT = 15000        # Maximum number of files to process (None for all)

AWS_ACCESS_KEY_ID = "********************"
AWS_SECRET_ACCESS_KEY = "xjG78e3yc0HRQN+Nm4DyB0Ky8XWigKYKIfdizCZ2"

# Processing options
SKIP_SCHEMA = True      # Skip database schema initialization
UPDATE_EXISTING = True   # Update existing records in database
LOG_LEVEL = "INFO"       # Logging level


def setup_logging(log_level: str = "INFO"):
    """Configure logging for the application"""
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)

    # Create filename with timestamp
    timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
    log_filename = f'logs/run_{timestamp}.log'

    # Clear any existing handlers
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    level = getattr(logging, log_level.upper(), logging.INFO)

    # File handler with UTF-8 encoding
    file_handler = logging.FileHandler(log_filename, encoding='utf-8')
    file_handler.setLevel(level)

    # Console handler with error handling for Unicode
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)

    # Formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # Add handlers to root logger
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    root_logger.setLevel(level)

    logger = logging.getLogger(__name__)
    logger.info(f"Logging initialized - File: {log_filename}")

    return log_filename


# Old inefficient functions removed - now using optimized S3Processor.process_from_s3()


def main():
    """Main application entry point"""
    # Setup logging
    setup_logging(LOG_LEVEL)
    logger = logging.getLogger(__name__)

    try:
        logger.info("Env Vars:")
        logger.info(f"DATABASE_URL: {os.getenv('DATABASE_URL')}")

        # Load configuration
        config = ProcessingConfig.from_env()

        # Initialize database schema (unless skipped)
        if not SKIP_SCHEMA:
            logger.info("🔧 Initializing database schema...")
            schema_manager = SchemaManager(config.connection_string)
            schema_manager.create_all_schemas(drop_existing=config.drop_schema)
        else:
            logger.info("⏭️  Skipping schema initialization")

        # Use optimized S3 processor with efficient file scanning
        processor = S3Processor(config, update_existing=UPDATE_EXISTING)

        logger.info("🚀 Starting optimized S3 processing")
        logger.info(
            f"📊 Target: {FILE_LIMIT or 'ALL'} files from s3://{S3_BUCKET}/{S3_PREFIX}")

        # Use the optimized process_from_s3 method with early termination
        success = processor.process_from_s3(
            bucket=S3_BUCKET,
            prefix=S3_PREFIX,
            limit=FILE_LIMIT
        )

        if success:
            logger.info("🎉 S3 processing completed successfully!")
            return 0
        else:
            logger.error("❌ S3 processing failed")
            return 1

    except KeyboardInterrupt:
        logger.info("🛑 Processing interrupted by user")
        return 130
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())


def cli():
    """CLI entry point for setuptools"""
    exit(main())
