# processors/core/territory_processor.py

from typing import Dict
from sqlalchemy.orm import Session
import logging

# Import SQLAlchemy models
from ddex_processor.models.generated import Territory
from ddex_processor.extractors.references.territory_extractor import TerritoryExtractor


class TerritoryProcessor:
    """SQLAlchemy-based processor for Territory entities"""

    def __init__(self, update_existing: bool = False):
        self.update_existing = update_existing
        self.logger = logging.getLogger(self.__class__.__name__)

    def process_territories(self, session: Session, ddex_data: Dict) -> Dict[str, Territory]:
        """Process territory entities"""
        self.logger.debug("Processing territories...")

        extractor = TerritoryExtractor(ddex_data)
        territory_data_list = extractor.extract()

        entity_map = {}

        for territory_data in territory_data_list:
            business_key = extractor.get_business_key(territory_data)
            territory = self._upsert_territory(session, territory_data)
            entity_map[business_key] = territory

        self.logger.info(f"Processed {len(entity_map)} territories")
        return entity_map

    def _upsert_territory(self, session: Session, territory_data: Dict) -> Territory:
        """Upsert a territory using territory_code lookup"""
        # Find existing territory by territory_code (unique constraint)
        existing = session.query(Territory).filter_by(
            territory_code=territory_data['territory_code']
        ).first()

        if existing and self.update_existing:
            # Update existing territory
            self.logger.debug(
                f"Updating existing territory: {existing.territory_code}")
            for key, value in territory_data.items():
                # Don't update primary key
                if hasattr(existing, key) and key not in ['id']:
                    setattr(existing, key, value)
            return existing

        elif existing:
            # Skip update, return existing
            self.logger.debug(
                f"Skipping existing territory: {existing.territory_code}")
            return existing

        else:
            # Create new territory
            self.logger.debug(
                f"Creating new territory: {territory_data.get('territory_code', 'Unknown')}")
            new_territory = Territory(**territory_data)
            session.add(new_territory)
            return new_territory

    def get_or_create_territory(self, session: Session, territory_code: str) -> Territory:
        """Get existing territory or create a minimal one if it doesn't exist"""
        existing = session.query(Territory).filter_by(
            territory_code=territory_code).first()

        if existing:
            return existing

        # Create minimal territory record
        territory_data = {
            'territory_code': territory_code,
            'territory_name': territory_code  # Default name to code
        }

        new_territory = Territory(**territory_data)
        session.add(new_territory)
        self.logger.debug(f"Auto-created territory: {territory_code}")
        return new_territory
