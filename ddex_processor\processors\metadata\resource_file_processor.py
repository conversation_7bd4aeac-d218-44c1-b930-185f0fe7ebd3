# processors/metadata/resource_file_processor.py

from typing import Dict, List
from sqlalchemy.orm import Session
import logging

# Import SQLAlchemy models
from ddex_processor.models.generated import ResourceFile, DDEXFile
from ddex_processor.extractors.metadata import ResourceFileExtractor


class ResourceFileProcessor:
    """SQLAlchemy-based processor for ResourceFile entities"""

    def __init__(self, update_existing: bool = False):
        self.update_existing = update_existing
        self.logger = logging.getLogger(self.__class__.__name__)

    def process_resource_files(self, session: Session, ddex_data: Dict, ddex_file: DDEXFile) -> Dict[str, ResourceFile]:
        """Process resource file entities"""
        self.logger.info("Processing resource files...")

        extractor = ResourceFileExtractor(ddex_data)
        resource_file_data_list = extractor.extract()

        entity_map = {}

        for resource_file_data in resource_file_data_list:
            business_key = extractor.get_business_key(resource_file_data)
            resource_file = self._upsert_resource_file(
                session, resource_file_data, ddex_file)
            entity_map[business_key] = resource_file

        self.logger.info(f"Processed {len(entity_map)} resource files")
        return entity_map

    def _upsert_resource_file(self, session: Session, resource_file_data: Dict, ddex_file: DDEXFile) -> ResourceFile:
        """Upsert a resource file using business key lookup"""

        # Clean data - remove fields that aren't part of the ResourceFile model
        clean_data = {k: v for k, v in resource_file_data.items()
                      if k in ['file_name', 'file_path', 'hash_sum', 'hash_sum_algorithm', 'codec_type']}

        # Find existing resource file by business key components (file_name + hash_sum)
        existing = session.query(ResourceFile).filter_by(
            file_name=clean_data.get('file_name'),
            hash_sum=clean_data.get('hash_sum')
        ).first()

        if existing and self.update_existing:
            # Update existing resource file
            self.logger.debug(
                f"Updating existing resource file: {existing.file_name}")
            for key, value in clean_data.items():
                if hasattr(existing, key) and key != 'id':
                    setattr(existing, key, value)
            existing.ddex_file_id = ddex_file.id  # Ensure ddex_file link is current
            return existing

        elif existing:
            # Skip update, return existing
            self.logger.debug(
                f"Skipping existing resource file: {existing.file_name}")
            return existing

        else:
            # Create new resource file
            self.logger.debug(
                f"Creating new resource file: {clean_data.get('file_name', 'Unknown')}")
            new_resource_file = ResourceFile(
                **clean_data,
                ddex_file_id=ddex_file.id  # Explicit foreign key!
            )
            session.add(new_resource_file)
            return new_resource_file

    def process_resource_file_metadata(self, session: Session, ddex_data: Dict, resource_file_map: Dict[str, ResourceFile]):
        """Process resource file metadata (for future link tables)"""
        self.logger.debug("Processing resource file metadata...")

        extractor = ResourceFileExtractor(ddex_data)
        metadata = extractor.extract_metadata()

        # Future: Process resource links when you create the link tables
        resource_links = metadata.get('resource_links', [])

        # For now, just log what links would be created
        self.logger.debug(
            f"Found {len(resource_links)} potential resource file links")
        for link in resource_links:
            self.logger.debug(
                f"Resource link: {link['file_business_key']} -> {link['resource_reference']} ({link['resource_type']})"
            )

        # TODO: Implement actual link processing when you create ResourceFileLinkProcessor
        # link_processor.process_resource_file_links(session, resource_links, resource_file_map)

    def get_resource_files_by_type(self, session: Session, codec_type: str) -> List[ResourceFile]:
        """Get all resource files of a specific type"""
        return session.query(ResourceFile).filter_by(codec_type=codec_type).all()

    def get_resource_files_by_ddex_file(self, session: Session, ddex_file_id: str) -> List[ResourceFile]:
        """Get all resource files from a specific DDEX file"""
        return session.query(ResourceFile).filter_by(ddex_file_id=ddex_file_id).all()

    def validate_file_integrity(self, session: Session, resource_file_id: str) -> bool:
        """Validate a file's integrity using stored hash (future implementation)"""
        resource_file = session.query(ResourceFile).filter_by(
            id=resource_file_id).first()

        if not resource_file:
            return False

        # TODO: Implement actual file validation
        # - Read file from file_path + file_name
        # - Calculate hash using hash_sum_algorithm
        # - Compare with stored hash_sum
        # - Return True if hashes match

        self.logger.info(
            f"File validation for {resource_file.file_name} - hash: {resource_file.hash_sum}")
        return True  # Placeholder

    def clear_resource_files_for_ddex_file(self, session: Session, ddex_file: DDEXFile):
        """Clear all resource files for a DDEX file (useful for reprocessing)"""
        count = session.query(ResourceFile).filter_by(
            ddex_file_id=ddex_file.id).delete()
        self.logger.debug(
            f"Cleared {count} resource files for DDEX file {ddex_file.id}")

    def get_file_statistics(self, session: Session) -> Dict[str, int]:
        """Get statistics about resource files"""
        from sqlalchemy import func

        stats = session.query(
            ResourceFile.codec_type,
            func.count(ResourceFile.id).label('count')
        ).group_by(ResourceFile.codec_type).all()

        return {codec_type: count for codec_type, count in stats}
