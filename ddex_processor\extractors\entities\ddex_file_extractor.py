# extractors/entities/ddex_file_extractor.py

from typing import List, Dict, Any
from ..base_extractor import BaseExtractor


class DDEXFileExtractor(BaseExtractor):
    """Extract DDEX file metadata from DDEX JSON with SQLAlchemy architecture"""

    def extract(self) -> List[Dict[str, Any]]:
        """DEPRECATED: Use extract_core_data() instead"""
        return self.extract_core_data()

    def extract_core_data(self) -> List[Dict[str, Any]]:
        """Extract CORE DDEX file metadata only (no relationships)"""
        self.logger.debug("Extracting DDEX file metadata")
        ddex_file_data = self._extract_ddex_file_metadata()
        return [ddex_file_data]

    def _extract_ddex_file_metadata(self) -> Dict[str, Any]:
        """Extract DDEX file metadata"""

        message_schema_version_id = self._safe_get(
            self.ddex_data, '@MessageSchemaVersionId', '')
        release_profile_version_id = self._safe_get(
            self.ddex_data, '@ReleaseProfileVersionId', '')
        message_header = self._safe_get(self.ddex_data, 'MessageHeader', {})
        update_indicator = self._safe_get(
            self.ddex_data, 'UpdateIndicator', '')

        message_thread_id = self._safe_get(
            message_header, 'MessageThreadId', '')
        message_id = self._safe_get(message_header, 'MessageId', '')
        message_filename = self._safe_get(
            message_header, 'MessageFileName', '')
        message_created_date = self._safe_get(
            message_header, 'MessageCreatedDateTime', '')
        message_control_type = self._safe_get(
            message_header, 'MessageControlType', '')

        message_sender = self._safe_get(message_header, 'MessageSender', {})
        message_sender_party_id = self._safe_get(message_sender, 'PartyId', '')
        message_sender_full_name = self._safe_get(
            message_sender, 'PartyName.FullName', '')

        sent_on_behalf_of = self._safe_get(
            message_header, 'SentOnBehalfOf', {})
        sent_on_behalf_of_party_id = self._safe_get(
            sent_on_behalf_of, 'PartyId', '')
        sent_on_behalf_of_full_name = self._safe_get(
            sent_on_behalf_of, 'PartyName.FullName', '')

        message_recipient = self._safe_get(
            message_header, 'MessageRecipient', {})
        message_recipient_name = self._safe_get(
            message_recipient, 'PartyName.FullName', '')

        return {
            'message_filename': message_filename,
            'message_id': message_id,
            'message_thread_id': message_thread_id,
            'message_control_type': message_control_type,
            'message_sender_party_id': message_sender_party_id,
            'message_sender_full_name': message_sender_full_name,
            'sent_on_behalf_of_party_id': sent_on_behalf_of_party_id,
            'sent_on_behalf_of_full_name': sent_on_behalf_of_full_name,
            'message_recipient': message_recipient_name,
            'update_indicator': update_indicator,
            'schema_version_id': message_schema_version_id,
            'message_created_date': message_created_date,
            'release_profile_version_id': release_profile_version_id
        }
