from typing import List, Dict, Any, Set
from ..base_extractor import SingleEntityExtractor


class TerritoryExtractor(SingleEntityExtractor):
    """Extract territory data from DDEX JSON"""

    def extract(self) -> List[Dict[str, Any]]:
        """Extract all unique territory codes from DDEX data"""
        territory_codes = set()

        # Extract from all possible locations
        territory_codes.update(self._extract_from_sound_recordings())
        territory_codes.update(self._extract_from_images())
        territory_codes.update(self._extract_from_releases())
        territory_codes.update(self._extract_from_deals())

        # Convert to list of dictionaries with territory metadata
        territories = []
        for territory_code in sorted(territory_codes):
            if territory_code:  # Skip empty/None values
                territories.append(self._create_territory_data(territory_code))

        self.logger.debug(f"Extracted {len(territories)} unique territories")
        return territories

    def _extract_from_sound_recordings(self) -> Set[str]:
        """Extract territory codes from ResourceList.SoundRecording"""
        territory_codes = set()

        sound_recordings = self._safe_get(
            self.ddex_data, 'ResourceList.SoundRecording', [])
        for recording in self._ensure_list(sound_recordings):
            # From SoundRecordingDetailsByTerritory
            details = self._safe_get(
                recording, 'SoundRecordingDetailsByTerritory')
            if details:
                territory_code = self._safe_get(details, 'TerritoryCode')
                if territory_code:
                    territory_codes.add(territory_code)

            # From TerritoryOfCommissioning
            commissioning_territory = self._safe_get(
                recording, 'TerritoryOfCommissioning')
            if commissioning_territory:
                territory_codes.add(commissioning_territory)

        return territory_codes

    def _extract_from_images(self) -> Set[str]:
        """Extract territory codes from ResourceList.Image"""
        territory_codes = set()

        images = self._safe_get(self.ddex_data, 'ResourceList.Image', [])
        for image in self._ensure_list(images):
            # From ImageDetailsByTerritory
            details = self._safe_get(image, 'ImageDetailsByTerritory')
            if details:
                territory_code = self._safe_get(details, 'TerritoryCode')
                if territory_code:
                    territory_codes.add(territory_code)

        return territory_codes

    def _extract_from_releases(self) -> Set[str]:
        """Extract territory codes from ReleaseList.Release"""
        territory_codes = set()

        releases = self._safe_get(self.ddex_data, 'ReleaseList.Release', [])
        for release in self._ensure_list(releases):
            # From ReleaseDetailsByTerritory
            details = self._safe_get(release, 'ReleaseDetailsByTerritory')
            if details:
                territory_code = self._safe_get(details, 'TerritoryCode')
                if territory_code:
                    territory_codes.add(territory_code)

        return territory_codes

    def _extract_from_deals(self) -> Set[str]:
        """Extract territory codes from DealList.ReleaseDeal"""
        territory_codes = set()

        deals_data = self._safe_get(self.ddex_data, 'DealList.ReleaseDeal', [])
        for deal_data in self._ensure_list(deals_data):
            deal_terms = self._safe_get(deal_data, 'Deal', [])
            for deal_term in self._ensure_list(deal_terms):
                # From ExcludedTerritoryCode
                excluded_territories = self._safe_get(
                    deal_term, 'DealTerms.ExcludedTerritoryCode', [])
                for territory in self._ensure_list(excluded_territories):
                    territory_code = territory if isinstance(
                        territory, str) else self._safe_get(territory, '#text', territory)
                    if territory_code:
                        territory_codes.add(territory_code)

                # From TerritoryCode (included territories)
                included_territories = self._safe_get(
                    deal_term, 'DealTerms.TerritoryCode', [])
                for territory in self._ensure_list(included_territories):
                    territory_code = territory if isinstance(
                        territory, str) else self._safe_get(territory, '#text', territory)
                    if territory_code:
                        territory_codes.add(territory_code)

        return territory_codes

    def _create_territory_data(self, territory_code: str) -> Dict[str, Any]:
        """Create territory data dictionary"""
        return {
            'territory_code': territory_code,
            'territory_name': territory_code,
        }

    def get_business_key(self, entity_data: Dict) -> str:
        """Generate business key for territory"""
        return entity_data['territory_code']

    def extract_core_data(self) -> List[Dict[str, Any]]:
        """Alias for extract() for consistency with other extractors"""
        return self.extract()

    def extract_metadata(self) -> Dict[str, List[Dict[str, Any]]]:
        """No metadata for territories - return empty dict"""
        return {}
