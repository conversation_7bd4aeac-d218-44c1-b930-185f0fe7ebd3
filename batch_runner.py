#!/usr/bin/env python3
"""
Simple batch runner for DDEX files from S3
"""

import boto3
import subprocess
import sys
import os

# Add the project root to Python path so imports work
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)


# Configuration - Edit these variables as needed
S3_BUCKET = "fuga-data-in"
S3_PREFIX = "rapier/"  # Folder prefix to search in
FILE_FILTER = ".xml"       # Only process files ending with this
FILE_LIMIT = 10          # Maximum number of files to process (None for all)
MAIN_SCRIPT = "main.py"    # Path to your main.py file

AWS_ACCESS_KEY_ID = "********************"
AWS_SECRET_ACCESS_KEY = "xjG78e3yc0HRQN+Nm4DyB0Ky8XWigKYKIfdizCZ2"


def get_s3_files():
    """Get list of files from S3 bucket"""
    s3 = boto3.client('s3', aws_access_key_id=AWS_ACCESS_KEY_ID,
                      aws_secret_access_key=AWS_SECRET_ACCESS_KEY)

    print(
        f"🔍 Looking for files in s3://{S3_BUCKET}/{S3_PREFIX} ending with {FILE_FILTER}")

    files = []
    paginator = s3.get_paginator('list_objects_v2')

    for page in paginator.paginate(Bucket=S3_BUCKET, Prefix=S3_PREFIX):
        if 'Contents' in page:
            for obj in page['Contents']:
                key: str = obj['Key']
                if key.endswith(FILE_FILTER) and not key.endswith('/') and 'Batch' not in key:
                    files.append(key)

    print(f"📁 Found {len(files)} files")

    # Apply file limit if specified
    if FILE_LIMIT and len(files) > FILE_LIMIT:
        files = files[:FILE_LIMIT]
        print(f"🔢 Limited to first {FILE_LIMIT} files")

    return files


def process_files(files):
    """Process each file using main.py"""
    total = len(files)
    successful = 0
    failed = 0

    for i, file_key in enumerate(files, 1):
        s3_uri = f"s3://{S3_BUCKET}/{file_key}"
        print(f"\n🔄 Processing {i}/{total}: {s3_uri}")

        try:
            # Run main.py with the S3 URI and proper Python path
            env = os.environ.copy()
            env['PYTHONPATH'] = project_root + \
                os.pathsep + env.get('PYTHONPATH', '')

            result = subprocess.run([
                sys.executable, '-m', 'ddex_processor.main', s3_uri
            ], capture_output=True, text=True, env=env, cwd=project_root)

            if result.returncode == 0:
                print(f"✅ Success: {file_key}")
                successful += 1
            else:
                print(f"❌ Failed: {file_key}")
                print(f"Error: {result.stderr}")
                failed += 1

        except Exception as e:
            print(f"❌ Error processing {file_key}: {e}")
            failed += 1

    print(
        f"\n📊 Summary: {successful} successful, {failed} failed out of {total} total")


if __name__ == "__main__":
    try:
        files = get_s3_files()
        if files:
            process_files(files)
        else:
            print("⚠️ No files found to process")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
