# extractors/entities/rights_controller_extractor.py

from typing import List, Dict, Any
from ..base_extractor import BaseExtractor


class RightsControllerExtractor(BaseExtractor):
    """Extract rights controller data from DDEX JSON with SQLAlchemy architecture"""

    def extract(self) -> List[Dict[str, Any]]:
        """DEPRECATED: Use extract_core_data() instead"""
        return self.extract_core_data()

    def extract_core_data(self) -> List[Dict[str, Any]]:
        """Extract CORE rights controller data only (no relationships)"""
        self.logger.debug("Extracting rights controllers")
        rights_controllers = set()

        # Extract from sound recordings
        sound_recordings = self._safe_get(
            self.ddex_data, 'ResourceList.SoundRecording', [])
        for recording in self._ensure_list(sound_recordings):
            rights_controllers.update(self._extract_from_recording(recording))

        # Convert set to list of dictionaries
        rights_controller_list = [
            {'party_id': party_id, 'name': name} for party_id, name in rights_controllers]
        self.logger.debug(
            f"Extracted {len(rights_controller_list)} unique rights controllers")

        return rights_controller_list

    def extract_metadata(self) -> Dict[str, List[Dict[str, Any]]]:
        """Extract relationship/metadata separately for Phase 2 processing"""
        # Rights controllers don't have metadata relationships in this system
        # Return empty dict to maintain consistency with the pattern
        return {
            'relationships': []  # Could add if you have rights controller relationships
        }

    def _extract_from_recording(self, recording: Dict) -> set:
        """Extract rights controllers from sound recording"""
        rights_controllers = set()
        rights_controller_data = self._safe_get(
            recording, 'SoundRecordingDetailsByTerritory.RightsController', []
        )

        for controller in self._ensure_list(rights_controller_data):
            party_id = self._safe_get(controller, 'PartyId')
            name = self._safe_get(controller, 'PartyName.FullName')

            if party_id and name:
                rights_controllers.add(
                    (party_id, name))

        return rights_controllers

    def get_business_key(self, entity_data: Dict) -> str:
        """Generate business key for rights controller"""
        return f"{entity_data['party_id']}|{entity_data['name']}"
