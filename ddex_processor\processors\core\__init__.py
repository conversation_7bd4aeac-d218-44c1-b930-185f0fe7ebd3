from .release_processor import ReleaseProcessor
from .track_processor import TrackProcessor
from .artist_processor import ArtistProcessor
from .role_processor import RoleProcessor
from .person_processor import PersonProcessor
from .publisher_processor import PublisherProcessor
from .rights_controller_processor import RightsControllerProcessor
from .territory_processor import TerritoryProcessor
from .ddex_file_processor import DDEXFileProcessor


__all__ = ['ReleaseProcessor', 'TrackProcessor',
           'ArtistProcessor', 'RoleProcessor',
           'PersonProcessor', 'PublisherProcessor', 'RightsControllerProcessor',
           'TerritoryProcessor', 'DDEXFileProcessor']
