from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Date, DateTime, Double, ForeignKeyConstraint, Index, Integer, PrimaryKeyConstraint, String, UniqueConstraint, Uuid, func
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship
import datetime
import uuid


class Base(DeclarativeBase):
    pass


class DealGroup(Base):
    __tablename__ = 'deal_group'
    __table_args__ = (
        ForeignKeyConstraint(['release_id'], [
                             'core.release.id'], name='deal_groups_release_id_fkey'),
        PrimaryKeyConstraint('id', name='deal_groups_pkey'),
        UniqueConstraint('deal_release_reference',
                         'effective_date', name='deal_group_unique'),
        {'schema': 'commercial'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    deal_release_reference: Mapped[str] = mapped_column(String)
    effective_date: Mapped[datetime.date] = mapped_column(Date)
    release_id: Mapped[uuid.UUID] = mapped_column(Uuid)

    deals: Mapped[List['Deal']] = relationship(
        'Deal', back_populates='deal_group')

    release: Mapped['Release'] = relationship(
        'Release', back_populates='deal_group')


class Artist(Base):
    __tablename__ = 'artist'
    __table_args__ = (
        PrimaryKeyConstraint('id', name='artist_pkey'),
        UniqueConstraint('name', name='artist_unique'),
        Index('idx_artist_name_lower', func.lower('name')),
        {'schema': 'core'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    name: Mapped[str] = mapped_column(String)

    release_artist_links: Mapped[List['ReleaseArtistLinks']] = relationship(
        'ReleaseArtistLinks', back_populates='artist')
    track_artist_links: Mapped[List['TrackArtistLinks']] = relationship(
        'TrackArtistLinks', back_populates='artist')


class DDEXFile(Base):
    __tablename__ = 'ddex_file'
    __table_args__ = (
        PrimaryKeyConstraint('id', name='ddex_file_pkey'),
        UniqueConstraint('message_filename', 'message_id',
                         name='ddex_file_unique'),
        {'schema': 'core'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    message_filename: Mapped[str] = mapped_column(String)
    message_id: Mapped[str] = mapped_column(String)
    message_thread_id: Mapped[str] = mapped_column(String)
    message_control_type: Mapped[str] = mapped_column(String)
    message_sender_party_id: Mapped[str] = mapped_column(String)
    message_sender_full_name: Mapped[str] = mapped_column(String)
    sent_on_behalf_of_party_id: Mapped[str] = mapped_column(String)
    sent_on_behalf_of_full_name: Mapped[str] = mapped_column(String)
    message_recipient: Mapped[str] = mapped_column(String)
    update_indicator: Mapped[str] = mapped_column(String)
    schema_version_id: Mapped[str] = mapped_column(String)
    message_created_date: Mapped[datetime.datetime] = mapped_column(DateTime)
    release_profile_version_id: Mapped[str] = mapped_column(String)

    resource_file: Mapped[List['ResourceFile']] = relationship(
        'ResourceFile', back_populates='ddex_file')


class Person(Base):
    __tablename__ = 'person'
    __table_args__ = (
        PrimaryKeyConstraint('id', name='person_pkey'),
        UniqueConstraint('name', name='person_unique'),
        {'schema': 'core'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    name: Mapped[str] = mapped_column(String)

    track_contributor_links: Mapped[List['TrackContributorLinks']] = relationship(
        'TrackContributorLinks', back_populates='contributor')


class Publisher(Base):
    __tablename__ = 'publisher'
    __table_args__ = (
        PrimaryKeyConstraint('id', name='publisher_pkey'),
        UniqueConstraint('name', name='publisher_unique'),
        {'schema': 'core'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    name: Mapped[str] = mapped_column(String)

    track_publisher_links: Mapped[List['TrackPublisherLinks']] = relationship(
        'TrackPublisherLinks', back_populates='publisher')


class Release(Base):
    __tablename__ = 'release'
    __table_args__ = (
        PrimaryKeyConstraint('id', name='release_pkey'),
        UniqueConstraint('upc', 'catalog_number', name='release_unique'),
        Index('idx_release_title_lower', func.lower('title')),
        Index('idx_release_upc', 'upc'),
        {'schema': 'core'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    upc: Mapped[int] = mapped_column(BigInteger)
    catalog_number: Mapped[str] = mapped_column(String)
    title: Mapped[str] = mapped_column(String)
    version: Mapped[Optional[str]] = mapped_column(String)
    display_artist_name: Mapped[str] = mapped_column(String)
    label_name: Mapped[str] = mapped_column(String)
    type: Mapped[Optional[str]] = mapped_column(String)
    parental_warning_type: Mapped[Optional[str]] = mapped_column(String)
    genre: Mapped[Optional[str]] = mapped_column(String)
    subgenre: Mapped[Optional[str]] = mapped_column(String)
    release_date: Mapped[Optional[datetime.date]] = mapped_column(Date)
    pline_year: Mapped[Optional[int]] = mapped_column(Integer)
    pline_text: Mapped[Optional[str]] = mapped_column(String)
    cline_year: Mapped[Optional[int]] = mapped_column(Integer)
    cline_text: Mapped[Optional[str]] = mapped_column(String)

    release_alternate_titles: Mapped[List['ReleaseAlternateTitles']] = relationship(
        'ReleaseAlternateTitles', back_populates='release')
    release_fuga: Mapped[List['ReleaseFuga']] = relationship(
        'ReleaseFuga', back_populates='release')
    release_artist_links: Mapped[List['ReleaseArtistLinks']] = relationship(
        'ReleaseArtistLinks', back_populates='release')
    release_track_links: Mapped[List['ReleaseTrackLinks']] = relationship(
        'ReleaseTrackLinks', back_populates='release')
    deal_group: Mapped['DealGroup'] = relationship(
        'DealGroup', back_populates='release')


class RightsController(Base):
    __tablename__ = 'rights_controller'
    __table_args__ = (
        PrimaryKeyConstraint('id', name='rights_controller_pkey'),
        UniqueConstraint('party_id', 'name', name='rights_controller_unique'),
        {'schema': 'core'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    party_id: Mapped[str] = mapped_column(String)
    name: Mapped[str] = mapped_column(String)

    rights_controller_links: Mapped[List['RightsControllerLinks']] = relationship(
        'RightsControllerLinks', back_populates='rights_controller')


class Role(Base):
    __tablename__ = 'role'
    __table_args__ = (
        PrimaryKeyConstraint('id', name='role_pkey'),
        UniqueConstraint('name', 'type', name='role_unique'),
        {'schema': 'core'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    name: Mapped[str] = mapped_column(String)
    type: Mapped[str] = mapped_column(String)

    release_artist_links: Mapped[List['ReleaseArtistLinks']] = relationship(
        'ReleaseArtistLinks', back_populates='role')
    track_artist_links: Mapped[List['TrackArtistLinks']] = relationship(
        'TrackArtistLinks', back_populates='role')
    track_contributor_links: Mapped[List['TrackContributorLinks']] = relationship(
        'TrackContributorLinks', back_populates='role')
    track_publisher_links: Mapped[List['TrackPublisherLinks']] = relationship(
        'TrackPublisherLinks', back_populates='role')


class Territory(Base):
    __tablename__ = 'territory'
    __table_args__ = (
        PrimaryKeyConstraint('id', name='territory_pkey'),
        UniqueConstraint('territory_code', name='territory_unique'),
        {'schema': 'core'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    territory_code: Mapped[str] = mapped_column(String)
    territory_name: Mapped[str] = mapped_column(String)

    deal_territories: Mapped[List['DealTerritories']] = relationship(
        'DealTerritories', back_populates='territory')


class Track(Base):
    __tablename__ = 'track'
    __table_args__ = (
        PrimaryKeyConstraint('id', name='track_pkey'),
        UniqueConstraint('isrc', 'catalogue', name='track_unique'),
        Index('idx_track_isrc', 'isrc'),
        Index('idx_track_title_lower', func.lower('title')),
        {'schema': 'core'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    catalogue: Mapped[str] = mapped_column(String)
    type: Mapped[str] = mapped_column(String)
    isrc: Mapped[str] = mapped_column(String)
    title: Mapped[str] = mapped_column(String)
    version: Mapped[Optional[str]] = mapped_column(String)
    display_artist_name: Mapped[str] = mapped_column(String)
    language_of_performance: Mapped[Optional[str]] = mapped_column(String)
    duration_iso: Mapped[Optional[str]] = mapped_column(String)
    duration_seconds: Mapped[Optional[int]] = mapped_column(Integer)
    label_name: Mapped[Optional[str]] = mapped_column(String)
    pline_year: Mapped[Optional[int]] = mapped_column(Integer)
    pline_text: Mapped[Optional[str]] = mapped_column(String)
    genre: Mapped[Optional[str]] = mapped_column(String)
    subgenre: Mapped[Optional[str]] = mapped_column(String)
    parental_warning_type: Mapped[Optional[str]] = mapped_column(String)

    track_alternate_titles: Mapped[List['TrackAlternateTitles']] = relationship(
        'TrackAlternateTitles', back_populates='track')
    track_fuga: Mapped[List['TrackFuga']] = relationship(
        'TrackFuga', back_populates='track')
    release_track_links: Mapped[List['ReleaseTrackLinks']] = relationship(
        'ReleaseTrackLinks', back_populates='track')
    rights_controller_links: Mapped[List['RightsControllerLinks']] = relationship(
        'RightsControllerLinks', back_populates='track')
    track_artist_links: Mapped[List['TrackArtistLinks']] = relationship(
        'TrackArtistLinks', back_populates='track')
    track_contributor_links: Mapped[List['TrackContributorLinks']] = relationship(
        'TrackContributorLinks', back_populates='track')
    track_publisher_links: Mapped[List['TrackPublisherLinks']] = relationship(
        'TrackPublisherLinks', back_populates='track')
    track_audio_links: Mapped[List['TrackAudioLinks']] = relationship(
        'TrackAudioLinks', back_populates='track')


class Deal(Base):
    __tablename__ = 'deal'
    __table_args__ = (
        ForeignKeyConstraint(['deal_group_id'], [
                             'commercial.deal_group.id'], name='deals_deal_group_id_fkey'),
        PrimaryKeyConstraint('id', name='deals_pkey'),
        Index('idx_deals_model_type', 'commercial_model_type'),
        {'schema': 'commercial'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    deal_group_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    commercial_model_type: Mapped[str] = mapped_column(String)
    start_date: Mapped[datetime.date] = mapped_column(Date)
    end_date: Mapped[Optional[str]]

    deal_group: Mapped['DealGroup'] = relationship(
        'DealGroup', back_populates='deals')
    deal_pricing: Mapped[List['DealPricing']] = relationship(
        'DealPricing', back_populates='deal')
    deal_territories: Mapped[List['DealTerritories']] = relationship(
        'DealTerritories', back_populates='deal')
    deal_usage_types: Mapped[List['DealUsageTypes']] = relationship(
        'DealUsageTypes', back_populates='deal')


class ReleaseAlternateTitles(Base):
    __tablename__ = 'release_alternate_titles'
    __table_args__ = (
        ForeignKeyConstraint(['release_id'], ['core.release.id'],
                             name='release_alternate_titles_release_id_fkey'),
        PrimaryKeyConstraint('id', name='release_alternate_titles_pkey'),
        UniqueConstraint('release_id', 'title', 'title_type',
                         name='release_alternate_title_unique'),
        {'schema': 'metadata'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    release_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    title: Mapped[str] = mapped_column(String)
    title_type: Mapped[str] = mapped_column(String)

    release: Mapped['Release'] = relationship(
        'Release', back_populates='release_alternate_titles')


class ReleaseFuga(Base):
    __tablename__ = 'release_fuga'
    __table_args__ = (
        ForeignKeyConstraint(['release_id'], ['core.release.id'],
                             name='release_fuga_release_id_fkey'),
        PrimaryKeyConstraint('id', name='release_fuga_pkey'),
        UniqueConstraint('release_id', 'fuga_id', name='release_fuga_unique'),
        {'schema': 'metadata'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    release_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    fuga_id: Mapped[str] = mapped_column(String)

    release: Mapped['Release'] = relationship(
        'Release', back_populates='release_fuga')


class ResourceFile(Base):
    __tablename__ = 'resource_file'
    __table_args__ = (
        ForeignKeyConstraint(['ddex_file_id'], [
                             'core.ddex_file.id'], name='resource_file_ddex_file_id_fkey'),
        PrimaryKeyConstraint('id', name='resource_file_pkey'),
        {'schema': 'metadata'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    ddex_file_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    file_name: Mapped[str] = mapped_column(String)
    file_path: Mapped[str] = mapped_column(String)
    hash_sum: Mapped[str] = mapped_column(String)
    hash_sum_algorithm: Mapped[str] = mapped_column(String)
    codec_type: Mapped[str] = mapped_column(String)

    ddex_file: Mapped['DDEXFile'] = relationship(
        'DDEXFile', back_populates='resource_file')
    track_audio_links: Mapped[List['TrackAudioLinks']] = relationship(
        'TrackAudioLinks', back_populates='resource_file')


class TrackAlternateTitles(Base):
    __tablename__ = 'track_alternate_titles'
    __table_args__ = (
        ForeignKeyConstraint(['track_id'], ['core.track.id'],
                             name='track_alternate_titles_track_id_fkey'),
        PrimaryKeyConstraint('id', name='track_alternate_titles_pkey'),
        UniqueConstraint('track_id', 'title', 'title_type',
                         name='track_alternate_title_unique'),
        {'schema': 'metadata'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    track_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    title: Mapped[str] = mapped_column(String)
    title_type: Mapped[str] = mapped_column(String)

    track: Mapped['Track'] = relationship(
        'Track', back_populates='track_alternate_titles')


class TrackFuga(Base):
    __tablename__ = 'track_fuga'
    __table_args__ = (
        ForeignKeyConstraint(['track_id'], ['core.track.id'],
                             name='track_fuga_track_id_fkey'),
        PrimaryKeyConstraint('id', name='track_fuga_pkey'),
        UniqueConstraint('track_id', 'fuga_id', name='track_fuga_unique'),
        {'schema': 'metadata'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    track_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    fuga_id: Mapped[str] = mapped_column(String)

    track: Mapped['Track'] = relationship('Track', back_populates='track_fuga')


class TrackAudioLinks(Base):
    __tablename__ = 'track_audio_links'
    __table_args__ = (
        ForeignKeyConstraint(['resource_file_id'], ['metadata.resource_file.id'],
                             name='track_audio_links_resource_file_id_fkey'),
        ForeignKeyConstraint(['track_id'], ['core.track.id'],
                             name='track_audio_links_track_id_fkey'),
        PrimaryKeyConstraint('id', name='track_audio_links_pkey'),
        UniqueConstraint('resource_file_id', 'track_id',
                         name='track_audio_links_unique'),
        {'schema': 'relations'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    resource_file_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    track_id: Mapped[uuid.UUID] = mapped_column(Uuid)

    resource_file: Mapped['ResourceFile'] = relationship(
        'ResourceFile', back_populates='track_audio_links')
    track: Mapped['Track'] = relationship(
        'Track', back_populates='track_audio_links')


class ReleaseArtistLinks(Base):
    __tablename__ = 'release_artist_links'
    __table_args__ = (
        ForeignKeyConstraint(['artist_id'], ['core.artist.id'],
                             name='release_artist_links_artist_id_fkey'),
        ForeignKeyConstraint(['release_id'], ['core.release.id'],
                             name='release_artist_links_release_id_fkey'),
        ForeignKeyConstraint(['role_id'], ['core.role.id'],
                             name='release_artist_links_role_id_fkey'),
        PrimaryKeyConstraint('id', name='release_artist_links_pkey'),
        UniqueConstraint('release_id', 'artist_id', 'role_id',
                         name='releases_artists_unique'),
        {'schema': 'relations'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    release_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    artist_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    role_id: Mapped[uuid.UUID] = mapped_column(Uuid)

    artist: Mapped['Artist'] = relationship(
        'Artist', back_populates='release_artist_links')
    release: Mapped['Release'] = relationship(
        'Release', back_populates='release_artist_links')
    role: Mapped['Role'] = relationship(
        'Role', back_populates='release_artist_links')


class ReleaseTrackLinks(Base):
    __tablename__ = 'release_track_links'
    __table_args__ = (
        ForeignKeyConstraint(['release_id'], ['core.release.id'],
                             name='release_track_links_release_id_fkey'),
        ForeignKeyConstraint(['track_id'], ['core.track.id'],
                             name='release_track_links_track_id_fkey'),
        PrimaryKeyConstraint('id', name='release_track_links_pkey'),
        UniqueConstraint('release_id', 'track_id', 'sequence',
                         'volume', name='releases_tracks_unique'),
        Index('idx_releases_tracks_release_id', 'release_id'),
        Index('idx_releases_tracks_track_id', 'track_id'),
        {'schema': 'relations'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    release_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    track_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    sequence: Mapped[int] = mapped_column(Integer)
    volume: Mapped[int] = mapped_column(Integer)

    release: Mapped['Release'] = relationship(
        'Release', back_populates='release_track_links')
    track: Mapped['Track'] = relationship(
        'Track', back_populates='release_track_links')


class RightsControllerLinks(Base):
    __tablename__ = 'rights_controller_links'
    __table_args__ = (
        ForeignKeyConstraint(['rights_controller_id'], [
                             'core.rights_controller.id'], name='rights_controller_links_rights_controller_id_fkey'),
        ForeignKeyConstraint(['track_id'], ['core.track.id'],
                             name='rights_controller_links_track_id_fkey'),
        PrimaryKeyConstraint('id', name='rights_controller_links_pkey'),
        UniqueConstraint('track_id', 'rights_controller_id',
                         'share_percentage', name='tracks_rights_controllers_unique'),
        {'schema': 'relations'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    track_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    rights_controller_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    share_percentage: Mapped[float] = mapped_column(Double(53))

    rights_controller: Mapped['RightsController'] = relationship(
        'RightsController', back_populates='rights_controller_links')
    track: Mapped['Track'] = relationship(
        'Track', back_populates='rights_controller_links')


class TrackArtistLinks(Base):
    __tablename__ = 'track_artist_links'
    __table_args__ = (
        ForeignKeyConstraint(['artist_id'], ['core.artist.id'],
                             name='track_artist_links_artist_id_fkey'),
        ForeignKeyConstraint(['role_id'], ['core.role.id'],
                             name='track_artist_links_role_id_fkey'),
        ForeignKeyConstraint(['track_id'], ['core.track.id'],
                             name='track_artist_links_track_id_fkey'),
        PrimaryKeyConstraint('id', name='track_artist_links_pkey'),
        UniqueConstraint('track_id', 'artist_id', 'role_id',
                         name='tracks_artists_unique'),
        {'schema': 'relations'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    track_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    artist_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    role_id: Mapped[uuid.UUID] = mapped_column(Uuid)

    artist: Mapped['Artist'] = relationship(
        'Artist', back_populates='track_artist_links')
    role: Mapped['Role'] = relationship(
        'Role', back_populates='track_artist_links')
    track: Mapped['Track'] = relationship(
        'Track', back_populates='track_artist_links')


class TrackContributorLinks(Base):
    __tablename__ = 'track_contributor_links'
    __table_args__ = (
        ForeignKeyConstraint(['contributor_id'], [
                             'core.person.id'], name='track_contributor_links_contributor_id_fkey'),
        ForeignKeyConstraint(['role_id'], ['core.role.id'],
                             name='track_contributor_links_role_id_fkey'),
        ForeignKeyConstraint(['track_id'], ['core.track.id'],
                             name='track_contributor_links_track_id_fkey'),
        PrimaryKeyConstraint('id', name='track_contributor_links_pkey'),
        UniqueConstraint('track_id', 'contributor_id',
                         'role_id', name='tracks_contributors_unique'),
        {'schema': 'relations'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    track_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    contributor_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    role_id: Mapped[uuid.UUID] = mapped_column(Uuid)

    contributor: Mapped['Person'] = relationship(
        'Person', back_populates='track_contributor_links')
    role: Mapped['Role'] = relationship(
        'Role', back_populates='track_contributor_links')
    track: Mapped['Track'] = relationship(
        'Track', back_populates='track_contributor_links')


class TrackPublisherLinks(Base):
    __tablename__ = 'track_publisher_links'
    __table_args__ = (
        ForeignKeyConstraint(['publisher_id'], [
                             'core.publisher.id'], name='track_publisher_links_publisher_id_fkey'),
        ForeignKeyConstraint(['role_id'], ['core.role.id'],
                             name='track_publisher_links_role_id_fkey'),
        ForeignKeyConstraint(['track_id'], ['core.track.id'],
                             name='track_publisher_links_track_id_fkey'),
        PrimaryKeyConstraint('id', name='track_publisher_links_pkey'),
        UniqueConstraint('track_id', 'publisher_id', 'role_id',
                         name='tracks_publishers_unique'),
        {'schema': 'relations'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    track_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    publisher_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    role_id: Mapped[uuid.UUID] = mapped_column(Uuid)

    publisher: Mapped['Publisher'] = relationship(
        'Publisher', back_populates='track_publisher_links')
    role: Mapped['Role'] = relationship(
        'Role', back_populates='track_publisher_links')
    track: Mapped['Track'] = relationship(
        'Track', back_populates='track_publisher_links')


class DealPricing(Base):
    __tablename__ = 'deal_pricing'
    __table_args__ = (
        ForeignKeyConstraint(
            ['deal_id'], ['commercial.deal.id'], name='deal_pricing_deal_id_fkey'),
        PrimaryKeyConstraint('id', name='deal_pricing_pkey'),
        {'schema': 'commercial'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    deal_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    price_range_type: Mapped[str] = mapped_column(String)
    price_type: Mapped[str] = mapped_column(String)

    deal: Mapped['Deal'] = relationship(
        'Deal', back_populates='deal_pricing')


class DealTerritories(Base):
    __tablename__ = 'deal_territory'
    __table_args__ = (
        ForeignKeyConstraint(['deal_id'], ['commercial.deal.id'],
                             name='deal_territories_deal_id_fkey'),
        ForeignKeyConstraint(['territory_code'], [
                             'core.territory.territory_code'], name='deal_territories_territory_code_fkey'),
        PrimaryKeyConstraint('id', name='deal_territories_pkey'),
        UniqueConstraint('deal_id', 'territory_code',
                         'is_excluded', name='deal_territory_unique'),
        {'schema': 'commercial'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    deal_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    territory_code: Mapped[str] = mapped_column(String)
    is_excluded: Mapped[bool] = mapped_column(Boolean)

    deal: Mapped['Deal'] = relationship(
        'Deal', back_populates='deal_territories')
    territory: Mapped['Territory'] = relationship(
        'Territory', back_populates='deal_territories')


class DealUsageTypes(Base):
    __tablename__ = 'deal_usage_type'
    __table_args__ = (
        ForeignKeyConstraint(['deal_id'], ['commercial.deal.id'],
                             name='deal_usage_types_deal_id_fkey'),
        PrimaryKeyConstraint('id', name='deal_usage_types_pkey'),
        UniqueConstraint('deal_id', 'usage_type', name='deal_usage_unique'),
        {'schema': 'commercial'}
    )

    id: Mapped[uuid.UUID] = mapped_column(
        Uuid, primary_key=True, default=uuid.uuid4)
    deal_id: Mapped[uuid.UUID] = mapped_column(Uuid)
    usage_type: Mapped[str] = mapped_column(String)

    deal: Mapped['Deal'] = relationship(
        'Deal', back_populates='deal_usage_types')
