# sqlmodelling/extractors/relations/track_audio_links_extractor.py

from typing import List, Dict, Any
from ..base_extractor import BaseExtractor


class TrackAudioLinksExtractor(BaseExtractor):
    """Extract track-resource file linking data from DDEX JSON"""

    def extract(self) -> List[Dict[str, Any]]:
        """Extract all track-audio file links from DDEX data"""
        self.logger.info("Extracting track-audio file links")
        links = []

        sound_recordings = self._safe_get(
            self.ddex_data, 'ResourceList.SoundRecording', []
        )

        for recording in self._ensure_list(sound_recordings):
            track_links = self._extract_links_for_recording(recording)
            links.extend(track_links)

        self.logger.info(f"Extracted {len(links)} track-audio file links")
        return links

    def _extract_links_for_recording(self, recording: Dict) -> List[Dict[str, Any]]:
        """Extract audio file links for a single track from a sound recording"""
        links = []

        # Get the ISRC which will be our track business key
        isrc = self._safe_get(recording, 'SoundRecordingId.ISRC')
        rights_controller_data = self._safe_get(
            recording, 'SoundRecordingDetailsByTerritory.RightsController')
        controller = self._ensure_list(rights_controller_data)[0]
        name = self._safe_get(controller, 'PartyName.FullName')
        track_business_key = f"{name}|{isrc}"
        if not track_business_key:
            self.logger.warning("No ISRC found for sound recording, skipping")
            return links

        # Get resource reference for this recording
        resource_reference = self._safe_get(recording, 'ResourceReference')
        if not resource_reference:
            self.logger.warning(
                f"No ResourceReference found for ISRC {track_business_key}")
            return links

        # Extract resource files for this recording
        resource_files = self._extract_resource_files_for_recording(recording)

        # Create links between the track and each resource file
        for resource_file_data in resource_files:
            resource_file_business_key = self._generate_resource_file_business_key(
                resource_file_data
            )

            if resource_file_business_key:
                links.append({
                    'track_business_key': track_business_key,
                    'resource_file_business_key': resource_file_business_key,
                    'resource_reference': resource_reference  # For additional context
                })

        return links

    def _extract_resource_files_for_recording(self, recording: Dict) -> List[Dict[str, Any]]:
        """Extract resource file data from a sound recording"""
        files = []

        # Get technical details for the sound recording
        sound_recording_details = self._safe_get(
            recording, 'SoundRecordingDetailsByTerritory'
        )

        # Handle both dict and list structures
        if isinstance(sound_recording_details, list):
            sound_recording_details = sound_recording_details[0] if sound_recording_details else {
            }

        # Extract technical sound recording details
        technical_details = self._safe_get(
            sound_recording_details, 'TechnicalSoundRecordingDetails'
        )

        if technical_details:
            # Handle both single file and multiple files
            file_info_list = self._safe_get(technical_details, 'File', [])

            for file_info in self._ensure_list(file_info_list):
                file_data = self._extract_file_data(file_info)
                if file_data:
                    files.append(file_data)

        return files

    def _extract_file_data(self, file_info: Dict) -> Dict[str, Any]:
        """Extract core file data from File object"""
        file_name = self._safe_get(file_info, 'FileName')
        if not file_name:
            return None

        file_path = self._safe_get(file_info, 'FilePath', '')

        # Extract hash information
        hash_info = self._safe_get(file_info, 'HashSum', {})
        hash_sum = self._safe_get(hash_info, 'HashSum', '')
        hash_algorithm = self._safe_get(hash_info, 'HashSumAlgorithmType', '')

        return {
            'file_name': file_name,
            'file_path': file_path,
            'hash_sum': hash_sum,
            'hash_sum_algorithm': hash_algorithm
        }

    def _generate_resource_file_business_key(self, file_data: Dict) -> str:
        """Generate business key for resource file (must match ResourceFileExtractor)"""
        # This must match the business key generation in ResourceFileExtractor
        file_name = file_data.get('file_name', '')
        hash_sum = file_data.get('hash_sum', '')
        return f"{file_name}|{hash_sum}"

    def get_business_key(self, link_data: Dict) -> str:
        """Generate business key for a track-audio file link"""
        # Combination of track and resource file business keys for uniqueness
        return f"{link_data['track_business_key']}|{link_data['resource_file_business_key']}"
