# processors/relations/release_artist_link_processor.py

from typing import Dict, List
from sqlalchemy.orm import Session
import logging

from ddex_processor.models.generated import ReleaseArtistLinks
from ddex_processor.extractors.relationships.release_artist_extractor import ReleaseArtistLinkExtractor


class ReleaseArtistLinkProcessor:
    """Processor for release-artist many-to-many relationships"""

    def __init__(self, update_existing: bool = False):
        self.update_existing = update_existing
        self.logger = logging.getLogger(self.__class__.__name__)

    def process_release_artist_links(
        self,
        session: Session,
        ddex_data: Dict,
        entity_maps: Dict
    ) -> bool:
        """Process all release-artist links"""
        self.logger.debug("Processing release-artist links...")

        try:
            # Extract link data
            extractor = ReleaseArtistLinkExtractor(ddex_data)
            link_data_list = extractor.extract()

            if not link_data_list:
                self.logger.debug("No release-artist links found")
                return True

            # Process each link
            processed_count = 0
            skipped_count = 0

            for link_data in link_data_list:
                if self._process_single_link(session, link_data, entity_maps):
                    processed_count += 1
                else:
                    skipped_count += 1

            self.logger.info(
                f"Processed {processed_count} release-artist links, "
                f"skipped {skipped_count} due to missing entities"
            )

            return True

        except Exception as e:
            self.logger.error(
                f"Error processing release-artist links: {str(e)}")
            return False

    def _process_single_link(
        self,
        session: Session,
        link_data: Dict,
        entity_maps: Dict
    ) -> bool:
        """Process a single release-artist link"""

        # Look up the entities
        release = entity_maps.get('releases', {}).get(
            link_data['release_business_key'])
        artist = entity_maps.get('artists', {}).get(
            link_data['artist_business_key'])
        role = entity_maps.get('roles', {}).get(link_data['role_business_key'])

        # Validate that all entities exist
        missing_entities = []

        if not release:
            missing_entities.append(
                f"release: {link_data['release_business_key']}")

        if not artist:
            missing_entities.append(
                f"artist: {link_data['artist_business_key']}")

        if not role:
            missing_entities.append(f"role: {link_data['role_business_key']}")

        if missing_entities:
            self.logger.warning(
                f"Skipping release-artist link due to missing entities: {', '.join(missing_entities)}"
            )
            return False

        # Create or update the link
        return self._upsert_link(session, release, artist, role, link_data)

    def _upsert_link(
        self,
        session: Session,
        release,
        artist,
        role,
        link_data: Dict
    ) -> bool:
        """Create or update a release-artist link"""
        try:
            # Check if link already exists
            existing_link = session.query(ReleaseArtistLinks).filter_by(
                release_id=release.id,
                artist_id=artist.id,
                role_id=role.id
            ).first()

            if existing_link:
                if self.update_existing:
                    # For this table, there's not much to update beyond the three IDs
                    # Could add fields like credit_text, sequence, etc. in the future
                    self.logger.debug(
                        f"Link already exists: Release {release.id} -> Artist {artist.id} "
                        f"(Role: {role.id})"
                    )
                    return True
                else:
                    # Skip existing link
                    self.logger.debug(
                        f"Skipping existing link: Release {release.id} -> Artist {artist.id}"
                    )
                    return True
            else:
                # Create new link
                new_link = ReleaseArtistLinks(
                    release_id=release.id,
                    artist_id=artist.id,
                    role_id=role.id
                )

                session.add(new_link)

                self.logger.debug(
                    f"Created link: Release {release.id} -> Artist {artist.id} "
                    f"(Role: {role.name})"
                )
                return True

        except Exception as e:
            self.logger.error(
                f"Error creating release-artist link: {str(e)}"
            )
            return False

    def clear_release_artist_links(self, session: Session, release_id: str):
        """Clear all artist links for a specific release (useful for full replacement)"""
        try:
            deleted_count = session.query(ReleaseArtistLinks).filter_by(
                release_id=release_id
            ).delete()

            self.logger.info(
                f"Cleared {deleted_count} artist links for release {release_id}")

        except Exception as e:
            self.logger.error(f"Error clearing release artist links: {str(e)}")

    def get_links_for_release(self, session: Session, release_id: str) -> List[ReleaseArtistLinks]:
        """Get all artist links for a specific release"""
        try:
            links = session.query(ReleaseArtistLinks).filter_by(
                release_id=release_id
            ).all()

            return links

        except Exception as e:
            self.logger.error(f"Error getting release artist links: {str(e)}")
            return []

    def get_links_for_artist(self, session: Session, artist_id: str) -> List[ReleaseArtistLinks]:
        """Get all release links for a specific artist"""
        try:
            links = session.query(ReleaseArtistLinks).filter_by(
                artist_id=artist_id
            ).all()

            return links

        except Exception as e:
            self.logger.error(f"Error getting artist release links: {str(e)}")
            return []

    def get_artists_for_release(self, session: Session, release_id: str, role_name: str = None) -> List[Dict]:
        """Get all artists for a release, optionally filtered by role"""
        try:
            from models.generated import Artist, Role

            query = session.query(
                ReleaseArtistLinks, Artist, Role
            ).join(
                Artist, ReleaseArtistLinks.artist_id == Artist.id
            ).join(
                Role, ReleaseArtistLinks.role_id == Role.id
            ).filter(
                ReleaseArtistLinks.release_id == release_id
            )

            if role_name:
                query = query.filter(Role.name == role_name)

            results = query.all()

            return [
                {
                    'artist': artist,
                    'role': role,
                    'link': link
                }
                for link, artist, role in results
            ]

        except Exception as e:
            self.logger.error(f"Error getting artists for release: {str(e)}")
            return []

    def validate_link_data(self, link_data: Dict) -> bool:
        """Validate that link data has all required fields"""
        required_fields = ['release_business_key',
                           'artist_business_key', 'role_business_key']

        for field in required_fields:
            if field not in link_data or not link_data[field]:
                self.logger.warning(
                    f"Missing required field '{field}' in link data")
                return False

        return True
