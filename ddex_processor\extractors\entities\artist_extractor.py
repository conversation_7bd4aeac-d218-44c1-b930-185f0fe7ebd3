# extractors/entities/artist_extractor.py

from typing import List, Dict, Any
from ddex_processor.extractors.base_extractor import BaseExtractor


class ArtistExtractor(BaseExtractor):
    """Extract artist data from DDEX JSON with SQLAlchemy architecture"""

    def extract(self) -> List[Dict[str, Any]]:
        """DEPRECATED: Use extract_core_data() instead"""
        return self.extract_core_data()

    def extract_core_data(self) -> List[Dict[str, Any]]:
        """Extract CORE artist data only (no relationships)"""
        self.logger.debug("Extracting artists")
        artists = set()

        # Extract from sound recordings
        sound_recordings = self._safe_get(
            self.ddex_data, 'ResourceList.SoundRecording', [])
        for recording in self._ensure_list(sound_recordings):
            artists.update(self._extract_from_recording(recording))

        # Extract from releases
        releases = self._safe_get(self.ddex_data, 'ReleaseList.Release', [])
        for release in self._ensure_list(releases):
            artists.update(self._extract_from_release(release))

        # Convert set to list of dictionaries
        artist_list = [{'name': name} for name in artists if name]
        self.logger.debug(f"Extracted {len(artist_list)} unique artists")

        return artist_list

    def _extract_from_recording(self, recording: Dict) -> set:
        """Extract artists from sound recording"""
        artists = set()

        # Get from SoundRecordingDetailsByTerritory
        recording_details = self._safe_get(
            recording, 'SoundRecordingDetailsByTerritory', {}
        )

        # Handle case where it might be a list
        if isinstance(recording_details, list):
            recording_details = recording_details[0] if recording_details else {
            }

        # Extract display artists
        display_artists = self._safe_get(
            recording_details, 'DisplayArtist', [])
        for artist in self._ensure_list(display_artists):
            name = self._extract_artist_name(artist)
            if name:
                artists.add(name)

        # Extract contributors (these might also be artists)
        contributors = self._safe_get(recording_details, 'Contributor', [])
        for contributor in self._ensure_list(contributors):
            name = self._extract_artist_name(contributor)
            if name:
                artists.add(name)

        return artists

    def _extract_from_release(self, release: Dict) -> set:
        """Extract artists from release"""
        artists = set()

        # Get from ReleaseDetailsByTerritory
        release_details = self._safe_get(
            release, 'ReleaseDetailsByTerritory', {}
        )

        # Handle case where it might be a list
        if isinstance(release_details, list):
            release_details = release_details[0] if release_details else {}

        # Extract display artists
        display_artists = self._safe_get(release_details, 'DisplayArtist', [])
        for artist in self._ensure_list(display_artists):
            name = self._extract_artist_name(artist)
            if name:
                artists.add(name)

        # Extract contributors
        contributors = self._safe_get(release_details, 'Contributor', [])
        for contributor in self._ensure_list(contributors):
            name = self._extract_artist_name(contributor)
            if name:
                artists.add(name)

        return artists

    def _extract_artist_name(self, artist_data: Dict) -> str:
        """Extract artist name from various possible structures"""
        # Try different possible name fields
        name_candidates = [
            self._safe_get(artist_data, 'PartyName.FullName'),
            self._safe_get(artist_data, 'ArtistName'),
            self._safe_get(artist_data, 'ContributorName'),
            self._safe_get(artist_data, 'PartyName.DisplayName'),
            self._safe_get(artist_data, 'Name')
        ]

        # Return the first non-empty name found
        for name in name_candidates:
            if name and isinstance(name, str) and name.strip():
                return name.strip()

        return None

    def get_business_key(self, entity_data: Dict) -> str:
        """Generate business key for artist"""
        return entity_data['name']
