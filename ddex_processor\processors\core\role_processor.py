# processors/core/role_processor.py

from typing import Dict, List
from sqlalchemy.orm import Session
import logging

# Import SQLAlchemy models (from generated file)
from ddex_processor.models.generated import Role
from ddex_processor.extractors.references.role_extractor import RoleExtractor


class RoleProcessor:
    """SQLAlchemy-based processor for Role entities"""

    def __init__(self, update_existing: bool = False):
        self.update_existing = update_existing
        self.logger = logging.getLogger(self.__class__.__name__)

    def process_roles(self, session: Session, ddex_data: Dict) -> Dict[str, Role]:
        """Process core role entities only (Phase 1)"""
        self.logger.debug("Processing roles...")

        extractor = RoleExtractor(ddex_data)
        role_data_list = extractor.extract_core_data()

        entity_map = {}

        for role_data in role_data_list:
            # Validate role data before processing
            if not self.validate_role_data(role_data):
                continue

            business_key = extractor.get_business_key(role_data)
            role = self._upsert_role(session, role_data)
            entity_map[business_key] = role

        self.logger.info(f"Processed {len(entity_map)} roles")
        return entity_map

    def _upsert_role(self, session: Session, role_data: Dict) -> Role:
        """Upsert a role using name+type lookup"""
        # Find existing role by business key (name + type)
        existing = session.query(Role).filter_by(
            name=role_data['name'],
            type=role_data['type']
        ).first()

        if existing:
            # Roles are reference data and rarely change
            # Return existing role without updating
            self.logger.debug(
                f"Found existing role: {existing.name} ({existing.type})")
            return existing
        else:
            # Create new role
            self.logger.debug(
                f"Creating new role: {role_data['name']} ({role_data['type']})")
            new_role = Role(**role_data)
            session.add(new_role)
            return new_role

    def get_role_by_name_and_type(self, session: Session, name: str, type: str) -> Role:
        """Get a role by name and type"""
        try:
            return session.query(Role).filter_by(name=name, type=type).first()
        except Exception as e:
            self.logger.error(
                f"Error getting role by name '{name}' and type '{type}': {str(e)}")
            return None

    def get_roles_by_type(self, session: Session, role_type: str) -> List[Role]:
        """Get all roles of a specific type"""
        try:
            return session.query(Role).filter_by(type=role_type).order_by(Role.name).all()
        except Exception as e:
            self.logger.error(
                f"Error getting roles by type '{role_type}': {str(e)}")
            return []

    def get_all_roles(self, session: Session) -> List[Role]:
        """Get all roles"""
        try:
            return session.query(Role).order_by(Role.type, Role.name).all()
        except Exception as e:
            self.logger.error(f"Error getting all roles: {str(e)}")
            return []

    def search_roles(self, session: Session, search_term: str) -> List[Role]:
        """Search roles by name (case-insensitive)"""
        try:
            return session.query(Role).filter(
                Role.name.ilike(f"%{search_term}%")
            ).order_by(Role.type, Role.name).all()
        except Exception as e:
            self.logger.error(f"Error searching roles: {str(e)}")
            return []

    def validate_role_data(self, role_data: Dict) -> bool:
        """Validate that role data has all required fields"""
        if 'name' not in role_data or not role_data['name']:
            self.logger.warning("Role data missing required 'name' field")
            return False

        if 'type' not in role_data or not role_data['type']:
            self.logger.warning("Role data missing required 'type' field")
            return False

        # Validate name is not just whitespace
        if not role_data['name'].strip():
            self.logger.warning("Role name cannot be empty or just whitespace")
            return False

        # Validate type is not just whitespace
        if not role_data['type'].strip():
            self.logger.warning("Role type cannot be empty or just whitespace")
            return False

        # Validate type is one of expected values
        valid_types = ['artist', 'contributor', 'publisher', 'label']
        if role_data['type'] not in valid_types:
            self.logger.warning(
                f"Invalid role type '{role_data['type']}'. Valid types: {valid_types}")
            # Don't reject - just warn, as DDEX might have other types

        # Check for reasonable length
        if len(role_data['name']) > 100:  # Adjust limit as needed
            self.logger.warning(
                f"Role name too long: {len(role_data['name'])} characters")
            return False

        return True

    def clean_role_data(self, role_data: Dict) -> Dict:
        """Clean and normalize role data"""
        cleaned = role_data.copy()

        # Strip whitespace
        if 'name' in cleaned:
            cleaned['name'] = cleaned['name'].strip()

        if 'type' in cleaned:
            cleaned['type'] = cleaned['type'].strip().lower()

        return cleaned

    def create_standard_roles(self, session: Session) -> Dict[str, Role]:
        """Create standard music industry roles if they don't exist"""
        standard_roles = [
            {'name': 'MainArtist', 'type': 'artist'},
            {'name': 'FeaturedArtist', 'type': 'artist'},
            {'name': 'Composer', 'type': 'contributor'},
            {'name': 'Lyricist', 'type': 'contributor'},
            {'name': 'Producer', 'type': 'contributor'},
            {'name': 'MusicPublisher', 'type': 'contributor'},
            {'name': 'Performer', 'type': 'contributor'},
            {'name': 'Vocalist', 'type': 'contributor'},
            {'name': 'Instrumentalist', 'type': 'contributor'},
        ]

        role_map = {}

        for role_data in standard_roles:
            if self.validate_role_data(role_data):
                business_key = f"{role_data['name']}|{role_data['type']}"
                role = self._upsert_role(session, role_data)
                role_map[business_key] = role

        self.logger.info(f"Created/verified {len(role_map)} standard roles")
        return role_map
