# sqlmodelling/processors/relations/track_audio_links_processor.py

from typing import Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import select

from ddex_processor.extractors.relations.track_audio_link_extractor import TrackAudioLinksExtractor
from ddex_processor.models.generated import TrackAudioLinks
import logging


class TrackAudioLinksProcessor:
    """Process track-audio file links"""

    def __init__(self, update_existing: bool = False):
        self.update_existing = update_existing
        self.logger = logging.getLogger(self.__class__.__name__)

    def process_track_audio_links(
        self,
        session: Session,
        ddex_data: Dict,
        entity_maps: Dict[str, Dict[str, Any]]
    ) -> bool:
        """Process track-audio file links using entity maps from Phase 1"""

        try:
            # Extract link data
            extractor = TrackAudioLinksExtractor(ddex_data)
            link_data_list = extractor.extract()

            if not link_data_list:
                self.logger.info("No track-audio file links to process")
                return True

            # Get entity maps
            track_map = entity_maps.get('tracks', {})
            resource_file_map = entity_maps.get('resource_files', {})

            if not track_map:
                self.logger.error("No tracks found in entity maps")
                return False

            if not resource_file_map:
                self.logger.error("No resource files found in entity maps")
                return False

            # Process each link
            links_created = 0
            links_skipped = 0

            for link_data in link_data_list:
                success = self._process_single_link(
                    session, link_data, track_map, resource_file_map
                )
                if success:
                    links_created += 1
                else:
                    links_skipped += 1

            self.logger.info(
                f"Track-audio links processed: {links_created} created, {links_skipped} skipped"
            )
            return True

        except Exception as e:
            self.logger.error(f"Failed to process track-audio links: {str(e)}")
            return False

    def _process_single_link(
        self,
        session: Session,
        link_data: Dict,
        track_map: Dict[str, Any],
        resource_file_map: Dict[str, Any]
    ) -> bool:
        """Process a single track-audio file link"""

        try:
            # Get business keys
            track_business_key = link_data.get('track_business_key')
            resource_file_business_key = link_data.get(
                'resource_file_business_key')

            if not track_business_key or not resource_file_business_key:
                self.logger.warning("Missing business keys in link data")
                return False

            # Find track entity
            track_entity = track_map.get(track_business_key)
            if not track_entity:
                self.logger.warning(
                    f"Track not found for business key: {track_business_key}")
                return False

            # Find resource file entity
            resource_file_entity = resource_file_map.get(
                resource_file_business_key)
            if not resource_file_entity:
                self.logger.warning(
                    f"Resource file not found for business key: {resource_file_business_key}")
                return False

            # Check if link already exists
            existing_link = session.execute(
                select(TrackAudioLinks).where(
                    TrackAudioLinks.track_id == track_entity.id,
                    TrackAudioLinks.resource_file_id == resource_file_entity.id
                )
            ).scalar_one_or_none()

            if existing_link:
                if self.update_existing:
                    self.logger.debug(
                        f"Link already exists between track {track_business_key} and resource file {resource_file_business_key}")
                else:
                    self.logger.debug(
                        f"Skipping existing link between track {track_business_key} and resource file {resource_file_business_key}")
                return True

            # Create new link
            new_link = TrackAudioLinks(
                track_id=track_entity.id,
                resource_file_id=resource_file_entity.id
            )

            session.add(new_link)
            self.logger.debug(
                f"Created link between track {track_business_key} and resource file {resource_file_business_key}")

            return True

        except Exception as e:
            self.logger.error(
                f"Failed to process single track-audio link: {str(e)}")
            return False
