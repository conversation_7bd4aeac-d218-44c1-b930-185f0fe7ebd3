# processors/core/person_processor.py

from typing import Dict, List
from sqlalchemy.orm import Session
import logging

# Import SQLAlchemy models (from generated file)
from ddex_processor.models.generated import Person
from ddex_processor.extractors.entities.person_extractor import PersonExtractor
from ddex_processor.db.batch_operations import BatchProcessor


class PersonProcessor:
    """SQLAlchemy-based processor for Person entities"""

    def __init__(self, update_existing: bool = False):
        self.update_existing = update_existing
        self.logger = logging.getLogger(self.__class__.__name__)

    def process_persons(self, session: Session, ddex_data: Dict) -> Dict[str, Person]:
        """Process core person entities only (Phase 1)"""
        self.logger.debug("Processing persons...")

        extractor = PersonExtractor(ddex_data)
        person_data_list = extractor.extract_core_data()

        # Filter valid person data
        valid_person_data = []
        for person_data in person_data_list:
            if self.validate_person_data(person_data):
                valid_person_data.append(person_data)

        # Always use batch processing for better performance
        return self._process_persons_batch(session, valid_person_data, extractor)

    def _process_persons_batch(self, session: Session, person_data_list: List[Dict], extractor: PersonExtractor) -> Dict[str, Person]:
        """Process persons using batch operations for better performance"""
        if not person_data_list:
            self.logger.info("Processed 0 persons")
            return {}

        batch_processor = BatchProcessor(session)

        # Perform bulk upsert using name as conflict column
        results = batch_processor.bulk_upsert(
            model_class=Person,
            data_list=person_data_list,
            conflict_columns=['name'],  # Persons are unique by name
            # Only update name if needed (persons rarely change)
            update_columns=['name']
        )

        # Build entity map using business keys
        entity_map = {}
        for i, person_data in enumerate(person_data_list):
            business_key = extractor.get_business_key(person_data)
            if i < len(results):
                # Create Person object from result row
                person = Person()
                for column in Person.__table__.columns:
                    if hasattr(results[i], column.name):
                        setattr(person, column.name, getattr(
                            results[i], column.name))
                entity_map[business_key] = person

        self.logger.info(f"Processed {len(entity_map)} persons")
        return entity_map

    def _upsert_person(self, session: Session, person_data: Dict) -> Person:
        """Upsert a person using name lookup"""
        # Find existing person by business key (name)
        existing = session.query(Person).filter_by(
            name=person_data['name']
        ).first()

        if existing and self.update_existing:
            # Update existing person (though persons rarely change)
            self.logger.debug(f"Updating existing person: {existing.name}")
            for key, value in person_data.items():
                if hasattr(existing, key) and key != 'id':
                    setattr(existing, key, value)
            return existing

        elif existing:
            # Skip update, return existing
            self.logger.debug(f"Skipping existing person: {existing.name}")
            return existing

        else:
            # Create new person
            self.logger.debug(
                f"Creating new person: {person_data.get('name', 'Unknown')}")
            new_person = Person(**person_data)
            session.add(new_person)
            return new_person

    def get_person_by_name(self, session: Session, name: str) -> Person:
        """Get a person by name"""
        try:
            return session.query(Person).filter_by(name=name).first()
        except Exception as e:
            self.logger.error(
                f"Error getting person by name '{name}': {str(e)}")
            return None

    def get_all_persons(self, session: Session) -> List[Person]:
        """Get all persons"""
        try:
            return session.query(Person).order_by(Person.name).all()
        except Exception as e:
            self.logger.error(f"Error getting all persons: {str(e)}")
            return []

    def search_persons(self, session: Session, search_term: str) -> List[Person]:
        """Search persons by name (case-insensitive)"""
        try:
            return session.query(Person).filter(
                Person.name.ilike(f"%{search_term}%")
            ).order_by(Person.name).all()
        except Exception as e:
            self.logger.error(f"Error searching persons: {str(e)}")
            return []

    def validate_person_data(self, person_data: Dict) -> bool:
        """Validate that person data has all required fields"""
        if 'name' not in person_data or not person_data['name']:
            self.logger.warning("Person data missing required 'name' field")
            return False

        # Validate name is not just whitespace
        if not person_data['name'].strip():
            self.logger.warning(
                "Person name cannot be empty or just whitespace")
            return False

        # Check for reasonable name length
        if len(person_data['name']) > 500:  # Adjust limit as needed
            self.logger.warning(
                f"Person name too long: {len(person_data['name'])} characters")
            return False

        # Basic check for valid name format (contains at least one letter)
        if not any(c.isalpha() for c in person_data['name']):
            self.logger.warning(
                f"Person name should contain at least one letter: {person_data['name']}")
            return False

        return True

    def clean_person_name(self, name: str) -> str:
        """Clean and normalize person name"""
        if not name:
            return ""

        # Strip whitespace
        cleaned = name.strip()

        # Remove multiple consecutive spaces
        import re
        cleaned = re.sub(r'\s+', ' ', cleaned)

        # Basic name capitalization (optional - might not always be desired)
        # cleaned = cleaned.title()

        return cleaned

    def get_persons_by_role(self, session: Session, role_name: str) -> List[Dict]:
        """Get all persons who have a specific role in track contributions"""
        try:
            from models.generated import TrackContributorLinks, Role

            query = session.query(
                Person, Role
            ).join(
                TrackContributorLinks, Person.id == TrackContributorLinks.contributor_id
            ).join(
                Role, TrackContributorLinks.role_id == Role.id
            ).filter(
                Role.name == role_name
            ).distinct()

            results = query.all()

            return [
                {
                    'person': person,
                    'role': role
                }
                for person, role in results
            ]

        except Exception as e:
            self.logger.error(
                f"Error getting persons by role '{role_name}': {str(e)}")
            return []

    def get_contribution_count(self, session: Session, person_id: str) -> int:
        """Get the number of track contributions for a person"""
        try:
            from models.generated import TrackContributorLinks

            count = session.query(TrackContributorLinks).filter_by(
                contributor_id=person_id
            ).count()

            return count

        except Exception as e:
            self.logger.error(
                f"Error getting contribution count for person {person_id}: {str(e)}")
            return 0

    def merge_persons(self, session: Session, keep_person_id: str, merge_person_id: str) -> bool:
        """Merge two person records (useful for duplicate cleanup)"""
        try:
            from models.generated import TrackContributorLinks

            # Update all track contributor links to point to the person we're keeping
            session.query(TrackContributorLinks).filter_by(
                contributor_id=merge_person_id
            ).update({
                'contributor_id': keep_person_id
            })

            # Delete the duplicate person
            session.query(Person).filter_by(id=merge_person_id).delete()

            self.logger.info(
                f"Merged person {merge_person_id} into {keep_person_id}")
            return True

        except Exception as e:
            self.logger.error(f"Error merging persons: {str(e)}")
            return False
