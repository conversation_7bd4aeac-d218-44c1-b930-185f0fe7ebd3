from typing import List, Dict, Any
from ..base_extractor import SingleEntityExtractor


class ResourceFileExtractor(SingleEntityExtractor):
    """Extract resource file data from DDEX JSON"""

    def extract(self) -> List[Dict[str, Any]]:
        """Extract all resource files from DDEX data"""
        resource_files = []

        # Extract from all resource types
        resource_files.extend(self._extract_sound_recording_files())
        resource_files.extend(self._extract_image_files())
        resource_files.extend(self._extract_text_files())

        self.logger.debug(f"Extracted {len(resource_files)} resource files")
        return resource_files

    def _extract_sound_recording_files(self) -> List[Dict[str, Any]]:
        """Extract files from sound recordings"""
        files = []

        sound_recordings = self._safe_get(
            self.ddex_data, 'ResourceList.SoundRecording', [])
        for recording in self._ensure_list(sound_recordings):
            resource_reference = self._safe_get(recording, 'ResourceReference')

            # Get technical details
            details = self._safe_get(
                recording, 'SoundRecordingDetailsByTerritory.TechnicalSoundRecordingDetails')
            if details:
                file_info = self._safe_get(details, 'File')
                if file_info:
                    file_data = self._extract_file_data(
                        file_info, resource_reference, 'sound_recording')

                    # Add audio-specific codec
                    codec_type = self._safe_get(details, 'AudioCodecType')
                    if codec_type:
                        file_data['codec_type'] = codec_type

                    files.append(file_data)

        return files

    def _extract_image_files(self) -> List[Dict[str, Any]]:
        """Extract files from images"""
        files = []

        images = self._safe_get(self.ddex_data, 'ResourceList.Image', [])
        for image in self._ensure_list(images):
            resource_reference = self._safe_get(image, 'ResourceReference')

            # Get technical details
            details = self._safe_get(
                image, 'ImageDetailsByTerritory.TechnicalImageDetails')
            if details:
                file_info = self._safe_get(details, 'File')
                if file_info:
                    file_data = self._extract_file_data(
                        file_info, resource_reference, 'image')

                    # Add image-specific codec
                    codec_type = self._safe_get(details, 'ImageCodecType')
                    if codec_type:
                        file_data['codec_type'] = codec_type

                    files.append(file_data)

        return files

    def _extract_text_files(self) -> List[Dict[str, Any]]:
        """Extract files from text resources"""
        files = []

        texts = self._safe_get(self.ddex_data, 'ResourceList.Text', [])
        for text in self._ensure_list(texts):
            resource_reference = self._safe_get(text, 'ResourceReference')

            # Get technical details
            details = self._safe_get(
                text, 'TextDetailsByTerritory.TechnicalTextDetails')
            if details:
                file_info = self._safe_get(details, 'File')
                if file_info:
                    file_data = self._extract_file_data(
                        file_info, resource_reference, 'text')

                    # Infer text codec from file extension or set default
                    if not file_data.get('codec_type'):
                        file_name = file_data.get('file_name', '')
                        if file_name.lower().endswith('.txt'):
                            file_data['codec_type'] = 'TEXT'
                        else:
                            file_data['codec_type'] = 'UNKNOWN'

                    files.append(file_data)

        return files

    def _extract_file_data(self, file_info: Dict, resource_reference: str, resource_type: str) -> Dict[str, Any]:
        """Extract core file data from File object"""
        file_name = self._safe_get(file_info, 'FileName')
        # Default to empty string
        file_path = self._safe_get(file_info, 'FilePath', '')

        # Extract hash information
        hash_info = self._safe_get(file_info, 'HashSum', {})
        hash_sum = self._safe_get(hash_info, 'HashSum', '')
        hash_algorithm = self._safe_get(hash_info, 'HashSumAlgorithmType', '')

        return {
            'file_name': file_name,
            'file_path': file_path,
            'hash_sum': hash_sum,
            'hash_sum_algorithm': hash_algorithm,
            'codec_type': '',  # Will be set by caller
            'resource_reference': resource_reference,  # For linking purposes
            'resource_type': resource_type  # For metadata
        }

    def get_business_key(self, entity_data: Dict) -> str:
        """Generate business key for resource file"""
        # Use file_name + hash_sum for uniqueness
        file_name = entity_data.get('file_name', '')
        hash_sum = entity_data.get('hash_sum', '')
        return f"{file_name}|{hash_sum}"

    def extract_core_data(self) -> List[Dict[str, Any]]:
        """Alias for extract() for consistency"""
        return self.extract()

    def extract_metadata(self) -> Dict[str, List[Dict[str, Any]]]:
        """Extract file-to-resource linking metadata"""
        files = self.extract()

        # Create linking data for separate processing
        links = []
        for file_data in files:
            if file_data.get('resource_reference'):
                links.append({
                    'file_business_key': self.get_business_key(file_data),
                    'resource_reference': file_data['resource_reference'],
                    'resource_type': file_data['resource_type']
                })

        return {
            'resource_links': links
        }
