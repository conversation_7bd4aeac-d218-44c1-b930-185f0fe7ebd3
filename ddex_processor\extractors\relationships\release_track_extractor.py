# extractors/relationships/release_track_extractor.py
from typing import List, Dict, Any, Optional
from .base_relationship_extractor import BaseRelationshipExtractor


class ReleaseTrackExtractor(BaseRelationshipExtractor):
    """Extract release-track relationship data"""

    def extract(self) -> List[Dict[str, Any]]:
        """Extract release-track relationships with resolved entities"""
        relationships = []

        releases = self._safe_get(self.ddex_data, 'ReleaseList.Release', [])
        for release in self._ensure_list(releases):
            if release.get('@IsMainRelease') != 'true':
                continue

            relationships.extend(self._extract_release_tracks(release))

        return relationships

    def _extract_release_tracks(self, release: Dict) -> List[Dict[str, Any]]:
        """Extract track relationships for a single release"""
        relationships = []

        # Get release business key and resolve to entity
        release_key = self._get_release_business_key(release)
        release_entity = self._resolve_entity('releases', release_key)

        if not release_entity:
            return relationships

        # Navigate to ResourceGroup structure
        release_details = self._safe_get(
            release, 'ReleaseDetailsByTerritory', {})
        resource_group = self._safe_get(
            release_details, 'ResourceGroup.ResourceGroup', {})

        # Get the disc/volume sequence number
        disc_sequence = self._safe_get(resource_group, 'SequenceNumber', '1')

        # Get the content items
        content_items = self._safe_get(
            resource_group, 'ResourceGroupContentItem', [])

        for item in self._ensure_list(content_items):
            resource_reference = self._safe_get(
                item, 'ReleaseResourceReference.#text')
            track_sequence = self._safe_get(item, 'SequenceNumber')

            if not resource_reference:
                self.logger.warning(
                    f"No resource reference found in content item: {item}")
                continue

            isrc = self._find_isrc_by_resource_reference(resource_reference)

            if not isrc:
                self.logger.warning(
                    f"Could not find ISRC for resource reference: {resource_reference}")
                continue

            # Resolve track by ISRC
            track_entity = self._resolve_entity('tracks', isrc)

            if track_entity:
                relationships.append({
                    'release_id': release_entity.id,
                    'track_id': track_entity.id,
                    'sequence': int(track_sequence) if track_sequence else 1,
                    'volume': int(disc_sequence) if disc_sequence else 1
                })
            else:
                self.logger.warning(
                    f"Could not resolve track entity for ISRC: {isrc} (resource ref: {resource_reference})"
                )

        return relationships

    def _find_isrc_by_resource_reference(self, resource_reference: str) -> Optional[str]:
        """Find ISRC by looking up resource reference in ResourceList"""
        try:
            # Get all sound recordings
            sound_recordings = self._safe_get(
                self.ddex_data, 'ResourceList.SoundRecording', [])

            for recording in self._ensure_list(sound_recordings):
                # Check if this recording has the matching resource reference
                recording_ref = self._safe_get(recording, 'ResourceReference')

                if recording_ref == resource_reference:
                    # Found the matching recording, get its ISRC
                    isrc = self._safe_get(recording, 'SoundRecordingId.ISRC')
                    if isrc:
                        return isrc
                    else:
                        self.logger.warning(
                            f"Found resource reference {resource_reference} but no ISRC")
                        return None

            # Resource reference not found
            self.logger.warning(
                f"Resource reference {resource_reference} not found in ResourceList")
            return None

        except Exception as e:
            self.logger.error(
                f"Error finding ISRC for resource reference {resource_reference}: {str(e)}")
            return None

    def _get_release_business_key(self, release: Dict) -> str:
        """Extract release business key"""
        upc = self._safe_get(release, 'ReleaseId.ICPN.#text')
        catalog_number = self._safe_get(
            release, 'ReleaseId.CatalogNumber.#text')
        return f"{upc}|{catalog_number}"

    def get_business_key(self, entity_data: Dict) -> str:
        """Generate business key for release-track relationship"""
        return f"{entity_data['release_id']}|{entity_data['track_id']}|{entity_data['sequence']}|{entity_data['volume']}"
