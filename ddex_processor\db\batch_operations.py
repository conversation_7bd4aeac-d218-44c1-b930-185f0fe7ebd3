# ddex_processor/db/batch_operations.py
"""
Batch database operations for improved performance with large datasets.
"""

import logging
from typing import List, Dict, Any, Type, Optional, Callable
from sqlalchemy.orm import Session
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy import select
from sqlalchemy.exc import IntegrityError

logger = logging.getLogger(__name__)


class BatchProcessor:
    """
    Utility class for efficient batch database operations.
    
    Provides methods for bulk inserts, upserts, and lookups to improve
    performance when processing large numbers of entities.
    """
    
    def __init__(self, session: Session, batch_size: int = 1000):
        self.session = session
        self.batch_size = batch_size
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def bulk_upsert(self, model_class: Type, data_list: List[Dict], 
                   conflict_columns: List[str], update_columns: List[str] = None) -> List:
        """
        Perform bulk upsert operation using PostgreSQL's ON CONFLICT.
        
        Args:
            model_class: SQLAlchemy model class
            data_list: List of dictionaries containing data to upsert
            conflict_columns: Columns to check for conflicts (unique constraints)
            update_columns: Columns to update on conflict (if None, updates all except conflict columns)
        
        Returns:
            List of created/updated objects
        """
        if not data_list:
            return []
        
        if update_columns is None:
            # Update all columns except conflict columns and id
            update_columns = [col.name for col in model_class.__table__.columns 
                            if col.name not in conflict_columns and col.name != 'id']
        
        results = []
        
        # Process in batches to avoid memory issues
        for i in range(0, len(data_list), self.batch_size):
            batch = data_list[i:i + self.batch_size]
            
            try:
                # Use PostgreSQL's INSERT ... ON CONFLICT for efficient upserts
                stmt = insert(model_class.__table__).values(batch)
                
                # Create update dictionary for ON CONFLICT
                update_dict = {col: stmt.excluded[col] for col in update_columns}
                
                stmt = stmt.on_conflict_do_update(
                    index_elements=conflict_columns,
                    set_=update_dict
                ).returning(model_class.__table__)
                
                result = self.session.execute(stmt)
                batch_results = result.fetchall()
                results.extend(batch_results)
                
                self.logger.debug(f"Bulk upserted {len(batch)} {model_class.__name__} records")
                
            except Exception as e:
                self.logger.error(f"Error in bulk upsert for {model_class.__name__}: {e}")
                # Fallback to individual operations for this batch
                results.extend(self._fallback_upsert(model_class, batch, conflict_columns))
        
        return results
    
    def _fallback_upsert(self, model_class: Type, data_list: List[Dict], 
                        conflict_columns: List[str]) -> List:
        """Fallback to individual upsert operations if bulk operation fails"""
        results = []
        
        for data in data_list:
            try:
                # Build filter conditions for existing record lookup
                filter_conditions = {col: data[col] for col in conflict_columns if col in data}
                existing = self.session.query(model_class).filter_by(**filter_conditions).first()
                
                if existing:
                    # Update existing record
                    for key, value in data.items():
                        if hasattr(existing, key) and key != 'id':
                            setattr(existing, key, value)
                    results.append(existing)
                else:
                    # Create new record
                    new_record = model_class(**data)
                    self.session.add(new_record)
                    results.append(new_record)
                    
            except Exception as e:
                self.logger.error(f"Error in fallback upsert for {model_class.__name__}: {e}")
                continue
        
        return results
    
    def bulk_insert_ignore_conflicts(self, model_class: Type, data_list: List[Dict]) -> int:
        """
        Perform bulk insert ignoring conflicts (INSERT ... ON CONFLICT DO NOTHING).
        
        Args:
            model_class: SQLAlchemy model class
            data_list: List of dictionaries containing data to insert
        
        Returns:
            Number of records actually inserted
        """
        if not data_list:
            return 0
        
        total_inserted = 0
        
        for i in range(0, len(data_list), self.batch_size):
            batch = data_list[i:i + self.batch_size]
            
            try:
                stmt = insert(model_class.__table__).values(batch)
                stmt = stmt.on_conflict_do_nothing()
                
                result = self.session.execute(stmt)
                total_inserted += result.rowcount
                
                self.logger.debug(f"Bulk inserted {result.rowcount} {model_class.__name__} records")
                
            except Exception as e:
                self.logger.error(f"Error in bulk insert for {model_class.__name__}: {e}")
                # Continue with next batch
                continue
        
        return total_inserted
    
    def bulk_lookup(self, model_class: Type, lookup_values: List[Any], 
                   lookup_column: str) -> Dict[Any, Any]:
        """
        Perform bulk lookup to get existing records by a specific column.
        
        Args:
            model_class: SQLAlchemy model class
            lookup_values: List of values to look up
            lookup_column: Column name to search by
        
        Returns:
            Dictionary mapping lookup values to found records
        """
        if not lookup_values:
            return {}
        
        results = {}
        
        # Process in batches to avoid query size limits
        for i in range(0, len(lookup_values), self.batch_size):
            batch_values = lookup_values[i:i + self.batch_size]
            
            try:
                column = getattr(model_class, lookup_column)
                query = self.session.query(model_class).filter(column.in_(batch_values))
                
                for record in query.all():
                    lookup_value = getattr(record, lookup_column)
                    results[lookup_value] = record
                    
            except Exception as e:
                self.logger.error(f"Error in bulk lookup for {model_class.__name__}: {e}")
                continue
        
        return results
