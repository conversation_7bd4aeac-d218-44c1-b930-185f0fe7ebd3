# extractors/relations/rights_controller_link_extractor.py

from typing import List, Dict, Any
from ..base_extractor import BaseExtractor


class RightsControllerLinkExtractor(BaseExtractor):
    """Extract track-rights controller linking data from DDEX JSON"""

    def extract(self) -> List[Dict[str, Any]]:
        """Extract all track-rights controller links from DDEX data"""
        self.logger.info("Extracting track-rights controller links")
        links = []

        sound_recordings = self._safe_get(
            self.ddex_data, 'ResourceList.SoundRecording', [])

        for recording in self._ensure_list(sound_recordings):
            track_links = self._extract_links_for_recording(recording)
            links.extend(track_links)

        self.logger.info(
            f"Extracted {len(links)} track-rights controller links")
        return links

    def _extract_links_for_recording(self, recording: Dict) -> List[Dict[str, Any]]:
        """Extract rights controller links for a single track"""
        links = []

        # Generate track business key
        track_business_key = self._get_business_key_from_recording(recording)
        if not track_business_key:
            self.logger.warning("No ISRC found for recording, skipping")
            return links

        # Get sound recording details (handle both dict and list structures)
        sound_recording_details = self._safe_get(
            recording, 'SoundRecordingDetailsByTerritory', {})
        if isinstance(sound_recording_details, list):
            sound_recording_details = sound_recording_details[0] if sound_recording_details else {
            }

        # Extract rights controllers
        rights_controllers = self._safe_get(
            sound_recording_details, 'RightsController', [])
        for controller_data in self._ensure_list(rights_controllers):
            controller_link = self._extract_single_rights_controller_link(
                controller_data, track_business_key
            )
            if controller_link:
                links.append(controller_link)

        return links

    def _extract_single_rights_controller_link(
        self,
        controller_data: Dict,
        track_business_key: str
    ) -> Dict[str, Any]:
        """Extract a single rights controller link from controller data"""

        # Get rights controller business key
        rights_controller_business_key = self._get_rights_controller_business_key(
            controller_data)
        if not rights_controller_business_key:
            self.logger.debug("No rights controller business key found")
            return None

        # Get share percentage
        share_percentage = self._safe_get(
            controller_data, 'RightSharePercentage')
        if not share_percentage:
            self.logger.debug("No share percentage found")
            return None

        return {
            'track_business_key': track_business_key,
            'rights_controller_business_key': rights_controller_business_key,
            'share_percentage': share_percentage
        }

    def _get_rights_controller_business_key(self, controller_data: Dict) -> str:
        """Generate business key for a rights controller from controller data"""
        party_id = self._safe_get(controller_data, 'PartyId')
        name = self._safe_get(controller_data, 'PartyName.FullName')
        return f"{party_id}|{name}"

    def get_business_key(self, link_data: Dict) -> str:
        """Generate business key for a track-rights controller link"""
        # Combination of all three entity business keys for uniqueness
        return f"{link_data['track_business_key']}|{link_data['rights_controller_business_key']}|{link_data['share_percentage']}"

    def _get_business_key_from_recording(self, recording: Dict) -> str:
        """Generate business key for a track from a sound recording"""
        rights_controller_data = self._safe_get(
            recording, 'SoundRecordingDetailsByTerritory.RightsController', []
        )

        controller = self._ensure_list(rights_controller_data)[0]
        name = self._safe_get(controller, 'PartyName.FullName')

        return f"{name}|{self._safe_get(recording, 'SoundRecordingId.ISRC')}"
