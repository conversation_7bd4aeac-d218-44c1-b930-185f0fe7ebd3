# processors/core/publisher_processor.py

from typing import Dict, List
from sqlalchemy.orm import Session
import logging

# Import SQLAlchemy models (from generated file)
from ddex_processor.models.generated import Publisher
from ddex_processor.extractors.entities.publisher_extractor import PublisherExtractor
from ddex_processor.db.batch_operations import BatchProcessor


class PublisherProcessor:
    """SQLAlchemy-based processor for Publisher entities"""

    def __init__(self, update_existing: bool = False):
        self.update_existing = update_existing
        self.logger = logging.getLogger(self.__class__.__name__)

    def process_publishers(self, session: Session, ddex_data: Dict) -> Dict[str, Publisher]:
        """Process core publisher entities only (Phase 1)"""
        self.logger.debug("Processing publishers...")

        extractor = PublisherExtractor(ddex_data)
        publisher_data_list = extractor.extract_core_data()

        # Filter valid publisher data
        valid_publisher_data = []
        for publisher_data in publisher_data_list:
            if self.validate_publisher_data(publisher_data):
                valid_publisher_data.append(publisher_data)

        # Always use batch processing for better performance
        return self._process_publishers_batch(session, valid_publisher_data, extractor)

    def _process_publishers_batch(self, session: Session, publisher_data_list: List[Dict], extractor: PublisherExtractor) -> Dict[str, Publisher]:
        """Process publishers using batch operations for better performance"""
        if not publisher_data_list:
            self.logger.info("Processed 0 publishers")
            return {}

        batch_processor = BatchProcessor(session)

        # Perform bulk upsert using name as conflict column
        results = batch_processor.bulk_upsert(
            model_class=Publisher,
            data_list=publisher_data_list,
            conflict_columns=['name'],  # Publishers are unique by name
            # Only update name if needed (publishers rarely change)
            update_columns=['name']
        )

        # Build entity map using business keys
        entity_map = {}
        for i, publisher_data in enumerate(publisher_data_list):
            business_key = extractor.get_business_key(publisher_data)
            if i < len(results):
                # Create Publisher object from result row
                publisher = Publisher()
                for column in Publisher.__table__.columns:
                    if hasattr(results[i], column.name):
                        setattr(publisher, column.name,
                                getattr(results[i], column.name))
                entity_map[business_key] = publisher

        self.logger.info(f"Processed {len(entity_map)} publishers")
        return entity_map

    def _upsert_publisher(self, session: Session, publisher_data: Dict) -> Publisher:
        """Upsert a publisher using name lookup"""
        # Find existing publisher by business key (name)
        existing = session.query(Publisher).filter_by(
            name=publisher_data['name']
        ).first()

        if existing and self.update_existing:
            # Update existing publisher (though publishers rarely change)
            self.logger.debug(f"Updating existing publisher: {existing.name}")
            for key, value in publisher_data.items():
                if hasattr(existing, key) and key != 'id':
                    setattr(existing, key, value)
            return existing

        elif existing:
            # Skip update, return existing
            self.logger.debug(f"Skipping existing publisher: {existing.name}")
            return existing

        else:
            # Create new publisher
            self.logger.debug(
                f"Creating new publisher: {publisher_data.get('name', 'Unknown')}")
            new_publisher = Publisher(**publisher_data)
            session.add(new_publisher)
            return new_publisher

    def get_publisher_by_name(self, session: Session, name: str) -> Publisher:
        """Get a publisher by name"""
        try:
            return session.query(Publisher).filter_by(name=name).first()
        except Exception as e:
            self.logger.error(
                f"Error getting publisher by name '{name}': {str(e)}")
            return None

    def get_all_publishers(self, session: Session) -> List[Publisher]:
        """Get all publishers"""
        try:
            return session.query(Publisher).order_by(Publisher.name).all()
        except Exception as e:
            self.logger.error(f"Error getting all publishers: {str(e)}")
            return []

    def search_publishers(self, session: Session, search_term: str) -> List[Publisher]:
        """Search publishers by name (case-insensitive)"""
        try:
            return session.query(Publisher).filter(
                Publisher.name.ilike(f"%{search_term}%")
            ).order_by(Publisher.name).all()
        except Exception as e:
            self.logger.error(f"Error searching publishers: {str(e)}")
            return []

    def validate_publisher_data(self, publisher_data: Dict) -> bool:
        """Validate that publisher data has all required fields"""
        if 'name' not in publisher_data or not publisher_data['name']:
            self.logger.warning("Publisher data missing required 'name' field")
            return False

        # Validate name is not just whitespace
        if not publisher_data['name'].strip():
            self.logger.warning(
                "Publisher name cannot be empty or just whitespace")
            return False

        # Check for reasonable name length
        if len(publisher_data['name']) > 500:  # Adjust limit as needed
            self.logger.warning(
                f"Publisher name too long: {len(publisher_data['name'])} characters")
            return False

        # Basic check for valid name format (contains at least one letter)
        if not any(c.isalpha() for c in publisher_data['name']):
            self.logger.warning(
                f"Publisher name should contain at least one letter: {publisher_data['name']}")
            return False

        return True

    def clean_publisher_name(self, name: str) -> str:
        """Clean and normalize publisher name"""
        if not name:
            return ""

        # Strip whitespace
        cleaned = name.strip()

        # Remove multiple consecutive spaces
        import re
        cleaned = re.sub(r'\s+', ' ', cleaned)

        return cleaned

    def get_publishers_by_role(self, session: Session, role_name: str) -> List[Dict]:
        """Get all publishers who have a specific role in track publishing"""
        try:
            from models.generated import TrackPublisherLinks, Role

            query = session.query(
                Publisher, Role
            ).join(
                TrackPublisherLinks, Publisher.id == TrackPublisherLinks.publisher_id
            ).join(
                Role, TrackPublisherLinks.role_id == Role.id
            ).filter(
                Role.name == role_name
            ).distinct()

            results = query.all()

            return [
                {
                    'publisher': publisher,
                    'role': role
                }
                for publisher, role in results
            ]

        except Exception as e:
            self.logger.error(
                f"Error getting publishers by role '{role_name}': {str(e)}")
            return []

    def get_publishing_count(self, session: Session, publisher_id: str) -> int:
        """Get the number of track publishing relationships for a publisher"""
        try:
            from models.generated import TrackPublisherLinks

            count = session.query(TrackPublisherLinks).filter_by(
                publisher_id=publisher_id
            ).count()

            return count

        except Exception as e:
            self.logger.error(
                f"Error getting publishing count for publisher {publisher_id}: {str(e)}")
            return 0

    def get_top_publishers_by_track_count(self, session: Session, limit: int = 10) -> List[Dict]:
        """Get publishers ranked by number of tracks they publish"""
        try:
            from models.generated import TrackPublisherLinks
            from sqlalchemy import func

            query = session.query(
                Publisher,
                func.count(TrackPublisherLinks.track_id).label('track_count')
            ).join(
                TrackPublisherLinks, Publisher.id == TrackPublisherLinks.publisher_id
            ).group_by(
                Publisher.id
            ).order_by(
                func.count(TrackPublisherLinks.track_id).desc()
            ).limit(limit)

            results = query.all()

            return [
                {
                    'publisher': publisher,
                    'track_count': track_count
                }
                for publisher, track_count in results
            ]

        except Exception as e:
            self.logger.error(f"Error getting top publishers: {str(e)}")
            return []

    def merge_publishers(self, session: Session, keep_publisher_id: str, merge_publisher_id: str) -> bool:
        """Merge two publisher records (useful for duplicate cleanup)"""
        try:
            from models.generated import TrackPublisherLinks

            # Update all track publisher links to point to the publisher we're keeping
            session.query(TrackPublisherLinks).filter_by(
                publisher_id=merge_publisher_id
            ).update({
                'publisher_id': keep_publisher_id
            })

            # Delete the duplicate publisher
            session.query(Publisher).filter_by(id=merge_publisher_id).delete()

            self.logger.info(
                f"Merged publisher {merge_publisher_id} into {keep_publisher_id}")
            return True

        except Exception as e:
            self.logger.error(f"Error merging publishers: {str(e)}")
            return False

    def create_standard_publishers(self, session: Session) -> Dict[str, Publisher]:
        """Create standard music publishers if they don't exist (useful for testing/setup)"""
        standard_publishers = [
            {'name': 'Unknown Publisher'},
            {'name': 'Self-Published'},
            {'name': 'Independent'},
            # Add more standard publishers as needed
        ]

        publisher_map = {}

        for publisher_data in standard_publishers:
            if self.validate_publisher_data(publisher_data):
                business_key = publisher_data['name']
                publisher = self._upsert_publisher(session, publisher_data)
                publisher_map[business_key] = publisher

        self.logger.info(
            f"Created/verified {len(publisher_map)} standard publishers")
        return publisher_map
