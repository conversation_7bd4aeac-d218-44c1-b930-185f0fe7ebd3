# extractors/entities/track_extractor.py
from typing import List, Dict, Any

# Remove SQLModel imports - we'll return plain data instead
from ..base_extractor import BaseExtractor


class TrackExtractor(BaseExtractor):
    """Extract track data from DDEX JSON with SQLAlchemy architecture"""

    def extract(self) -> List[Dict[str, Any]]:
        """DEPRECATED: Use extract_core_data() instead"""
        return self.extract_core_data()

    def extract_core_data(self) -> List[Dict[str, Any]]:
        """Extract CORE track data only (no relationships)"""
        self.logger.debug("Extracting tracks")
        tracks = []

        sound_recordings = self._safe_get(
            self.ddex_data, 'ResourceList.SoundRecording', []
        )
        for recording in self._ensure_list(sound_recordings):
            tracks.append(self._extract_core_track_data(recording))

        return tracks

    def extract_metadata(self) -> Dict[str, List[Dict[str, Any]]]:
        """Extract relationship/metadata separately for Phase 2 processing"""
        sound_recordings = self._safe_get(
            self.ddex_data, 'ResourceList.SoundRecording', []
        )

        fuga_data = []
        alternate_titles_data = []

        for recording in self._ensure_list(sound_recordings):
            business_key = self._get_business_key_from_recording(recording)

            # Extract Fuga data
            fuga_id = self._safe_get(
                recording, 'SoundRecordingId.ProprietaryId.#text')
            if fuga_id:
                fuga_data.append({
                    'track_business_key': business_key,
                    'fuga_id': fuga_id
                })

            # Extract alternate titles data
            sound_recording_details = self._safe_get(
                recording, 'SoundRecordingDetailsByTerritory', {}
            )
            if isinstance(sound_recording_details, list):
                sound_recording_details = sound_recording_details[0] if sound_recording_details else {
                }

            titles = self._safe_get(sound_recording_details, 'Title', [])
            for title in self._ensure_list(titles):
                title_text = self._safe_get(title, 'TitleText')
                title_type = self._safe_get(title, '@TitleType')
                if title_text:
                    alternate_titles_data.append({
                        'track_business_key': business_key,
                        'title': title_text,
                        'title_type': title_type
                    })

        return {
            'fuga': fuga_data,
            'alternate_titles': alternate_titles_data
        }

    def _extract_core_track_data(self, recording: Dict) -> Dict[str, Any]:
        """Extract ONLY core track fields (no relationships)"""
        isrc = self._safe_get(recording, 'SoundRecordingId.ISRC')
        title = self._safe_get(recording, 'ReferenceTitle.TitleText')
        version = self._safe_get(recording, 'ReferenceTitle.SubTitle', None)
        type = self._safe_get(recording, 'SoundRecordingType')
        language_of_performance = self._safe_get(
            recording, 'LanguageOfPerformance')
        duration_iso = self._safe_get(recording, 'Duration')
        duration_seconds = self._convert_duration_to_seconds(duration_iso)

        # Get territory details
        sound_recording_details = self._safe_get(
            recording, 'SoundRecordingDetailsByTerritory', {}
        )
        if isinstance(sound_recording_details, list):
            sound_recording_details = sound_recording_details[0] if sound_recording_details else {
            }

        display_artist_name = self._safe_get(
            sound_recording_details, 'DisplayArtistName')
        label_name = self._safe_get(sound_recording_details, 'LabelName')
        pline_year = self._safe_get(sound_recording_details, 'PLine.Year')
        pline_text = self._safe_get(sound_recording_details, 'PLine.PLineText')
        genre = self._safe_get(sound_recording_details, 'Genre.GenreText')
        subgenre = self._safe_get(sound_recording_details, 'Genre.SubGenre')
        parental_warning_type = self._safe_get(
            sound_recording_details, 'ParentalWarningType')
        rights_controller = self._safe_get(
            sound_recording_details, 'RightsController.PartyName.FullName')

        # Return ONLY core fields - no relationships!
        return {
            'isrc': isrc,
            'title': title,
            'version': version,
            'type': type,
            'language_of_performance': language_of_performance,
            'duration_iso': duration_iso,
            'duration_seconds': duration_seconds,
            'display_artist_name': display_artist_name,
            'label_name': label_name,
            'pline_year': pline_year,
            'pline_text': pline_text,
            'genre': genre,
            'subgenre': subgenre,
            'parental_warning_type': parental_warning_type,
            'catalogue': rights_controller
        }

    def _convert_duration_to_seconds(self, duration: str) -> int:
        """Convert ISO 8601 duration to seconds"""
        if not duration:
            return 0

        try:
            import re
            match = re.match(r'PT(?:(\d+)M)?(?:(\d+)S)?', duration)
            if match:
                minutes = int(match.group(1) or 0)
                seconds = int(match.group(2) or 0)
                total_seconds = minutes * 60 + seconds
                return total_seconds
            else:
                self.logger.warning(
                    f"Could not parse ISO duration: {duration}")
                return 0
        except Exception as e:
            self.logger.error(
                f"Error parsing ISO duration '{duration}': {str(e)}")
            raise

    def _get_business_key_from_recording(self, recording: Dict) -> str:
        """Generate business key from recording dict"""
        rights_controller_data = self._safe_get(
            recording, 'SoundRecordingDetailsByTerritory.RightsController', []
        )

        controller = self._ensure_list(rights_controller_data)[0]
        name = self._safe_get(controller, 'PartyName.FullName')

        return f"{name}|{self._safe_get(recording, 'SoundRecordingId.ISRC')}"

    def get_business_key(self, entity_data: Dict) -> str:
        """Generate business key from entity data dict"""
        return f"{entity_data['catalogue']}|{entity_data['isrc']}"
