# ddex_processor/db/connection_manager.py
"""
Centralized database connection manager with optimized connection pooling
for AWS RDS t3.micro instances.
"""

import logging
from typing import Optional, ContextManager
from sqlalchemy import create_engine, Engine, event
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.pool import QueuePool
import threading
import time

logger = logging.getLogger(__name__)


class DatabaseManager:
    """
    Centralized database connection manager with optimized pooling for RDS t3.micro.

    Provides shared connection pool across all application components to maximize
    efficiency and prevent connection exhaustion.
    """

    _instance: Optional['DatabaseManager'] = None
    _lock = threading.Lock()

    def __init__(self, connection_string: str):
        if DatabaseManager._instance is not None:
            raise RuntimeError(
                "DatabaseManager is a singleton. Use get_instance() instead.")

        self.connection_string = connection_string
        self._engine: Optional[Engine] = None
        self._session_factory: Optional[sessionmaker] = None
        self._setup_engine()

    @classmethod
    def get_instance(cls, connection_string: str = None) -> 'DatabaseManager':
        """Get singleton instance of DatabaseManager"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    if connection_string is None:
                        raise ValueError(
                            "connection_string required for first initialization")
                    cls._instance = cls(connection_string)
        return cls._instance

    @classmethod
    def reset_instance(cls):
        """Reset singleton instance (mainly for testing)"""
        with cls._lock:
            if cls._instance and cls._instance._engine:
                cls._instance._engine.dispose()
            cls._instance = None

    def _setup_engine(self):
        """Setup SQLAlchemy engine with optimized connection pool settings"""

        # Optimized settings for AWS RDS t3.micro (112 max connections)
        # Conservative but efficient approach: use 5-15 connections max
        pool_settings = {
            'poolclass': QueuePool,
            'pool_size': 5,              # Base persistent connections
            # Additional connections when needed (total: 15)
            'max_overflow': 10,
            'pool_timeout': 60,          # Wait time for connection (seconds)
            'pool_recycle': 1800,        # Recycle connections every 30 minutes
            # Validate connections before use (good for RDS)
            'pool_pre_ping': True,
            'pool_reset_on_return': 'commit',  # Clean state on return
        }

        # Additional engine settings for performance
        engine_settings = {
            'echo': False,               # Set to True for SQL debugging
            'future': True,              # Use SQLAlchemy 2.0 style
            'connect_args': {
                'connect_timeout': 30,   # Connection timeout
                'application_name': 'ddex_processor'
            }
        }

        # Combine settings
        all_settings = {**pool_settings, **engine_settings}

        self._engine = create_engine(self.connection_string, **all_settings)

        # Setup event listeners for monitoring
        self._setup_event_listeners()

        # Create session factory
        self._session_factory = sessionmaker(
            bind=self._engine,
            expire_on_commit=False,  # Keep objects accessible after commit
            autoflush=True,          # Auto-flush before queries
            autocommit=False         # Explicit transaction control
        )

        logger.info(f"Database engine initialized with pool_size={pool_settings['pool_size']}, "
                    f"max_overflow={pool_settings['max_overflow']}")

    def _setup_event_listeners(self):
        """Setup SQLAlchemy event listeners for monitoring and optimization"""

        @event.listens_for(self._engine, "connect")
        def set_postgresql_session_params(dbapi_connection, connection_record):
            """Set session-level optimizations (PostgreSQL specific)"""
            if 'postgresql' in self.connection_string:
                with dbapi_connection.cursor() as cursor:
                    # Only set session-level parameters that don't require server restart
                    # These can be changed per connection without affecting the server
                    # Session-level, safe
                    cursor.execute("SET synchronous_commit = OFF")
                    # Session-level, safe for bulk ops
                    cursor.execute("SET work_mem = '4MB'")
                    # Session-level, safe
                    cursor.execute("SET maintenance_work_mem = '64MB'")
                    # Removed wal_buffers and checkpoint_completion_target - these require server restart

        @event.listens_for(self._engine, "checkout")
        def receive_checkout(dbapi_connection, connection_record, connection_proxy):
            """Log connection checkout for monitoring"""
            logger.debug("Connection checked out from pool")

        @event.listens_for(self._engine, "checkin")
        def receive_checkin(dbapi_connection, connection_record):
            """Log connection checkin for monitoring"""
            logger.debug("Connection returned to pool")

    @property
    def engine(self) -> Engine:
        """Get the SQLAlchemy engine"""
        if self._engine is None:
            raise RuntimeError("Engine not initialized")
        return self._engine

    def get_session(self) -> Session:
        """Create a new database session"""
        if self._session_factory is None:
            raise RuntimeError("Session factory not initialized")
        return self._session_factory()

    def get_session_context(self) -> ContextManager[Session]:
        """Get a session context manager that auto-closes"""
        return self.get_session()

    def execute_with_retry(self, operation, max_retries: int = 3, delay: float = 1.0):
        """
        Execute database operation with retry logic for transient failures

        Args:
            operation: Callable that takes a session parameter
            max_retries: Maximum number of retry attempts
            delay: Delay between retries in seconds
        """
        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                with self.get_session() as session:
                    result = operation(session)
                    session.commit()
                    return result

            except Exception as e:
                last_exception = e
                logger.warning(
                    f"Database operation failed (attempt {attempt + 1}/{max_retries + 1}): {e}")

                if attempt < max_retries:
                    time.sleep(delay * (2 ** attempt))  # Exponential backoff
                else:
                    logger.error(
                        f"Database operation failed after {max_retries + 1} attempts")
                    raise last_exception

    def get_pool_status(self) -> dict:
        """Get current connection pool status for monitoring"""
        if self._engine is None:
            return {"status": "not_initialized"}

        pool = self._engine.pool
        return {
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid()
        }

    def close(self):
        """Close all connections and dispose of the engine"""
        if self._engine:
            logger.info(
                "Disposing database engine and closing all connections")
            self._engine.dispose()
            self._engine = None
            self._session_factory = None


# Convenience functions for backward compatibility
def get_database_manager(connection_string: str = None) -> DatabaseManager:
    """Get the singleton DatabaseManager instance"""
    return DatabaseManager.get_instance(connection_string)


def get_engine(connection_string: str = None) -> Engine:
    """Get the shared SQLAlchemy engine"""
    return get_database_manager(connection_string).engine


def get_session(connection_string: str = None) -> Session:
    """Get a new database session"""
    return get_database_manager(connection_string).get_session()
