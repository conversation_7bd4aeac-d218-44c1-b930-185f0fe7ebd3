from abc import ABC, abstractmethod
from typing import Dict, List, Any
import logging


class BaseExtractor(ABC):
    """Abstract base class for data extractors"""

    def __init__(self, ddex_data: Dict):
        self.ddex_data = ddex_data
        self.logger = logging.getLogger(self.__class__.__name__)

    @abstractmethod
    def extract(self) -> List[Dict[str, Any]]:
        """Extract entities from DDEX data"""
        pass

    def get_business_key(self, entity_data: Dict) -> str:
        """Generate business key for entity (optional - for single-entity extractors)"""
        raise NotImplementedError(
            f"{self.__class__.__name__} does not implement get_business_key(). "
            "This method is optional for multi-entity extractors."
        )

    def _safe_get(self, data: Dict, path: str, default: Any = None) -> Any:
        """Safely navigate nested dictionary paths"""
        keys = path.split('.')
        current = data
        try:
            for key in keys:
                current = current[key]
            return current
        except (KeyError, TypeError):
            return default

    def _ensure_list(self, data: Any) -> List:
        """Ensure data is a list"""
        if data is None:
            return []
        return data if isinstance(data, list) else [data]


class SingleEntityExtractor(BaseExtractor):
    """Base class for extractors that handle a single entity type"""

    @abstractmethod
    def get_business_key(self, entity_data: Dict) -> str:
        """Generate business key for entity (required for single-entity extractors)"""
        pass


class MultiEntityExtractor(BaseExtractor):
    """Base class for extractors that handle multiple entity types"""

    def get_business_key(self, entity_data: Dict) -> str:
        """Not applicable for multi-entity extractors"""
        raise NotImplementedError(
            f"{self.__class__.__name__} handles multiple entities. "
            "Use specific business key methods for each entity type."
        )
