# ddex_procesor/config/settings.py
from dataclasses import dataclass
import os

from dotenv import load_dotenv

load_dotenv()


@dataclass
class ProcessingConfig:
    """Configuration for DDEX processing"""
    connection_string: str
    drop_schema: bool = False
    batch_size: int = 1000
    enable_validation: bool = True
    log_level: str = "INFO"
    # Concurrent processing options
    enable_concurrent: bool = True  # Enable by default for better performance
    max_workers: int = 4  # Slightly increased for better throughput

    @classmethod
    def from_env(cls) -> 'ProcessingConfig':
        """Create config from environment variables"""

        return cls(
            connection_string=os.getenv('DATABASE_URL', ''),
            drop_schema=os.getenv('DROP_SCHEMA', 'false').lower() == 'true',
            batch_size=int(os.getenv('BATCH_SIZE', '1000')),
            enable_validation=os.getenv(
                'ENABLE_VALIDATION', 'true').lower() == 'true',
            log_level=os.getenv('LOG_LEVEL', 'INFO'),
            enable_concurrent=os.getenv(
                'ENABLE_CONCURRENT', 'false').lower() == 'true',
            max_workers=int(os.getenv('MAX_WORKERS', '3'))
        )
