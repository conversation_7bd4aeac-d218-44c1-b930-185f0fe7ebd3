# processors/relations/track_contributor_link_processor.py

from typing import Dict, List
from sqlalchemy.orm import Session
import logging

from ddex_processor.models.generated import TrackContributorLinks
from ddex_processor.extractors.relations.track_contributor_link_extractor import TrackContributorLinkExtractor


class TrackContributorLinkProcessor:
    """Processor for track-contributor many-to-many relationships (excludes publishers)"""

    def __init__(self, update_existing: bool = False):
        self.update_existing = update_existing
        self.logger = logging.getLogger(self.__class__.__name__)

    def process_track_contributor_links(
        self,
        session: Session,
        ddex_data: Dict,
        entity_maps: Dict
    ) -> bool:
        """Process all track-contributor links"""
        self.logger.debug("Processing track-contributor links...")

        try:
            # Extract link data
            extractor = TrackContributorLinkExtractor(ddex_data)
            link_data_list = extractor.extract()

            if not link_data_list:
                self.logger.debug("No track-contributor links found")
                return True

            # Process each link
            processed_count = 0
            skipped_count = 0

            for link_data in link_data_list:
                if self._process_single_link(session, link_data, entity_maps):
                    processed_count += 1
                else:
                    skipped_count += 1

            self.logger.info(
                f"Processed {processed_count} track-contributor links, "
                f"skipped {skipped_count} due to missing entities"
            )

            return True

        except Exception as e:
            self.logger.error(
                f"Error processing track-contributor links: {str(e)}")
            return False

    def _process_single_link(
        self,
        session: Session,
        link_data: Dict,
        entity_maps: Dict
    ) -> bool:
        """Process a single track-contributor link"""

        # Look up the entities
        track = entity_maps.get('tracks', {}).get(
            link_data['track_business_key'])
        # Note: Contributors are stored as "persons" not "artists"
        contributor = entity_maps.get('persons', {}).get(
            link_data['contributor_business_key'])
        role = entity_maps.get('roles', {}).get(link_data['role_business_key'])

        # Validate that all entities exist
        missing_entities = []

        if not track:
            missing_entities.append(
                f"track: {link_data['track_business_key']}")

        if not contributor:
            missing_entities.append(
                f"contributor: {link_data['contributor_business_key']}")

        if not role:
            missing_entities.append(f"role: {link_data['role_business_key']}")

        if missing_entities:
            self.logger.warning(
                f"Skipping track-contributor link due to missing entities: {', '.join(missing_entities)}"
            )
            return False

        # Create or update the link
        return self._upsert_link(session, track, contributor, role, link_data)

    def _upsert_link(
        self,
        session: Session,
        track,
        contributor,
        role,
        link_data: Dict
    ) -> bool:
        """Create or update a track-contributor link"""
        try:
            # Check if link already exists
            existing_link = session.query(TrackContributorLinks).filter_by(
                track_id=track.id,
                contributor_id=contributor.id,
                role_id=role.id
            ).first()

            if existing_link:
                if self.update_existing:
                    # For this table, there's not much to update beyond the three IDs
                    # Could add fields like credit_text, percentage, etc. in the future
                    self.logger.debug(
                        f"Link already exists: Track {track.id} -> Contributor {contributor.id} "
                        f"(Role: {role.id})"
                    )
                    return True
                else:
                    # Skip existing link
                    self.logger.debug(
                        f"Skipping existing link: Track {track.id} -> Contributor {contributor.id}"
                    )
                    return True
            else:
                # Create new link
                new_link = TrackContributorLinks(
                    track_id=track.id,
                    contributor_id=contributor.id,
                    role_id=role.id
                )

                session.add(new_link)

                self.logger.debug(
                    f"Created link: Track {track.id} -> Contributor {contributor.id} "
                    f"(Role: {role.name})"
                )
                return True

        except Exception as e:
            self.logger.error(
                f"Error creating track-contributor link: {str(e)}"
            )
            return False

    def clear_track_contributor_links(self, session: Session, track_id: str):
        """Clear all contributor links for a specific track (useful for full replacement)"""
        try:
            deleted_count = session.query(TrackContributorLinks).filter_by(
                track_id=track_id
            ).delete()

            self.logger.info(
                f"Cleared {deleted_count} contributor links for track {track_id}")

        except Exception as e:
            self.logger.error(
                f"Error clearing track contributor links: {str(e)}")

    def get_links_for_track(self, session: Session, track_id: str) -> List[TrackContributorLinks]:
        """Get all contributor links for a specific track"""
        try:
            links = session.query(TrackContributorLinks).filter_by(
                track_id=track_id
            ).all()

            return links

        except Exception as e:
            self.logger.error(
                f"Error getting track contributor links: {str(e)}")
            return []

    def get_links_for_contributor(self, session: Session, contributor_id: str) -> List[TrackContributorLinks]:
        """Get all track links for a specific contributor"""
        try:
            links = session.query(TrackContributorLinks).filter_by(
                contributor_id=contributor_id
            ).all()

            return links

        except Exception as e:
            self.logger.error(
                f"Error getting contributor track links: {str(e)}")
            return []

    def get_contributors_for_track(self, session: Session, track_id: str, role_name: str = None) -> List[Dict]:
        """Get all contributors for a track, optionally filtered by role"""
        try:
            from models.generated import Person, Role

            query = session.query(
                TrackContributorLinks, Person, Role
            ).join(
                Person, TrackContributorLinks.contributor_id == Person.id
            ).join(
                Role, TrackContributorLinks.role_id == Role.id
            ).filter(
                TrackContributorLinks.track_id == track_id
            )

            if role_name:
                query = query.filter(Role.name == role_name)

            results = query.all()

            return [
                {
                    'contributor': person,
                    'role': role,
                    'link': link
                }
                for link, person, role in results
            ]

        except Exception as e:
            self.logger.error(
                f"Error getting contributors for track: {str(e)}")
            return []

    def get_contributors_by_role(self, session: Session, track_id: str) -> Dict[str, List]:
        """Get contributors for a track grouped by role"""
        try:
            contributors_data = self.get_contributors_for_track(
                session, track_id)

            grouped = {}
            for item in contributors_data:
                role_name = item['role'].name
                if role_name not in grouped:
                    grouped[role_name] = []
                grouped[role_name].append(item['contributor'])

            return grouped

        except Exception as e:
            self.logger.error(f"Error getting contributors by role: {str(e)}")
            return {}

    def get_tracks_for_contributor(self, session: Session, contributor_id: str, role_name: str = None) -> List[Dict]:
        """Get all tracks for a contributor, optionally filtered by role"""
        try:
            from models.generated import Track, Role

            query = session.query(
                TrackContributorLinks, Track, Role
            ).join(
                Track, TrackContributorLinks.track_id == Track.id
            ).join(
                Role, TrackContributorLinks.role_id == Role.id
            ).filter(
                TrackContributorLinks.contributor_id == contributor_id
            )

            if role_name:
                query = query.filter(Role.name == role_name)

            results = query.all()

            return [
                {
                    'track': track,
                    'role': role,
                    'link': link
                }
                for link, track, role in results
            ]

        except Exception as e:
            self.logger.error(
                f"Error getting tracks for contributor: {str(e)}")
            return []

    def validate_link_data(self, link_data: Dict) -> bool:
        """Validate that link data has all required fields"""
        required_fields = ['track_business_key',
                           'contributor_business_key', 'role_business_key']

        for field in required_fields:
            if field not in link_data or not link_data[field]:
                self.logger.warning(
                    f"Missing required field '{field}' in link data")
                return False

        return True
