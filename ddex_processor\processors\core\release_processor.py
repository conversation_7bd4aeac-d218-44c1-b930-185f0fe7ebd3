# processors/core/release_processor.py

from typing import Dict, List
from sqlalchemy.orm import Session
import logging

# Import SQLAlchemy models (from generated file)
from ddex_processor.models.generated import Release, ReleaseFuga, ReleaseAlternateTitles
from ddex_processor.extractors.entities.release_extractor import ReleaseExtractor
from ddex_processor.db.batch_operations import BatchProcessor


class ReleaseProcessor:
    """SQLAlchemy-based processor for Release entities and their metadata"""

    def __init__(self, update_existing: bool = False):
        self.update_existing = update_existing
        self.logger = logging.getLogger(self.__class__.__name__)

    def process_releases(self, session: Session, ddex_data: Dict) -> Dict[str, Release]:
        """Process core release entities only (Phase 1)"""
        self.logger.debug("Processing releases...")

        extractor = ReleaseExtractor(ddex_data)
        release_data_list = extractor.extract_core_data()

        # Always use batch processing for better performance
        return self._process_releases_batch(session, release_data_list, extractor)

    def _process_releases_batch(self, session: Session, release_data_list: List[Dict], extractor: ReleaseExtractor) -> Dict[str, Release]:
        """Process releases using batch operations for better performance"""
        if not release_data_list:
            self.logger.info("Processed 0 releases")
            return {}

        batch_processor = BatchProcessor(session)

        # Perform bulk upsert using UPC + catalog_number as conflict columns
        results = batch_processor.bulk_upsert(
            model_class=Release,
            data_list=release_data_list,
            # Releases are unique by UPC + catalog_number
            conflict_columns=['upc', 'catalog_number'],
            update_columns=['title', 'version', 'display_artist_name', 'label_name', 'type',
                            'parental_warning_type', 'genre', 'subgenre', 'release_date',
                            'pline_year', 'pline_text', 'cline_year', 'cline_text']
        )

        # Build entity map using business keys
        entity_map = {}
        for i, release_data in enumerate(release_data_list):
            business_key = extractor.get_business_key(release_data)
            if i < len(results):
                # Create Release object from result row
                release = Release()
                for column in Release.__table__.columns:
                    if hasattr(results[i], column.name):
                        setattr(release, column.name, getattr(
                            results[i], column.name))
                entity_map[business_key] = release

        self.logger.info(f"Processed {len(entity_map)} releases")
        return entity_map

    def process_release_metadata(self, session: Session, ddex_data: Dict, release_map: Dict[str, Release]):
        """Process release metadata relationships (Phase 2)"""
        self.logger.info("Processing release metadata...")

        extractor = ReleaseExtractor(ddex_data)
        metadata = extractor.extract_metadata()

        # Process Fuga relationships
        fuga_count = 0
        for fuga_data in metadata.get('fuga', []):
            release_key = fuga_data['release_business_key']
            if release_key in release_map:
                release = release_map[release_key]
                self._upsert_release_fuga(session, release, fuga_data)
                fuga_count += 1

        # Process alternate titles
        title_count = 0
        for title_data in metadata.get('alternate_titles', []):
            release_key = title_data['release_business_key']
            if release_key in release_map:
                release = release_map[release_key]
                self._upsert_release_alternate_title(
                    session, release, title_data)
                title_count += 1

        self.logger.info(
            f"Processed {fuga_count} fuga records and {title_count} alternate titles")

    def _upsert_release(self, session: Session, release_data: Dict) -> Release:
        """Upsert a release using business key lookup"""
        # Find existing release by business key (upc + catalog_number)
        existing = session.query(Release).filter_by(
            upc=release_data['upc'],
            catalog_number=release_data['catalog_number']
        ).first()

        if existing and self.update_existing:
            # Update existing release
            self.logger.debug(f"Updating existing release: {existing.title}")
            for key, value in release_data.items():
                if hasattr(existing, key) and key != 'id':
                    setattr(existing, key, value)
            return existing

        elif existing:
            # Skip update, return existing
            self.logger.debug(f"Skipping existing release: {existing.title}")
            return existing

        else:
            # Create new release
            self.logger.debug(
                f"Creating new release: {release_data.get('title', 'Unknown')}")
            new_release = Release(**release_data)
            session.add(new_release)
            return new_release

    def _upsert_release_fuga(self, session: Session, release: Release, fuga_data: Dict):
        """Upsert release fuga metadata with explicit foreign key"""
        existing = session.query(ReleaseFuga).filter_by(
            release_id=release.id
        ).first()

        if existing:
            # Update existing fuga record
            existing.fuga_id = fuga_data['fuga_id']
            self.logger.debug(f"Updated fuga for release {release.id}")
        else:
            # Create new fuga record
            new_fuga = ReleaseFuga(
                release_id=release.id,  # Explicit foreign key!
                fuga_id=fuga_data['fuga_id']
            )
            session.add(new_fuga)
            self.logger.debug(f"Created fuga for release {release.id}")

    def _upsert_release_alternate_title(self, session: Session, release: Release, title_data: Dict):
        """Upsert release alternate title with explicit foreign key"""
        # Check if this specific title already exists
        existing = session.query(ReleaseAlternateTitles).filter_by(
            release_id=release.id,
            title=title_data['title'],
            title_type=title_data['title_type']
        ).first()

        if not existing:
            # Create new alternate title
            new_title = ReleaseAlternateTitles(
                release_id=release.id,  # Explicit foreign key!
                title=title_data['title'],
                title_type=title_data['title_type']
            )
            session.add(new_title)
            self.logger.debug(
                f"Created alternate title for release {release.id}: {title_data['title']}")

    def clear_release_alternate_titles(self, session: Session, release: Release):
        """Clear all alternate titles for a release (useful for full replacement)"""
        session.query(ReleaseAlternateTitles).filter_by(
            release_id=release.id
        ).delete()
        self.logger.debug(
            f"Cleared all alternate titles for release {release.id}")

    def replace_release_alternate_titles(self, session: Session, release: Release, titles_data: List[Dict]):
        """Replace all alternate titles for a release"""
        # Clear existing titles
        self.clear_release_alternate_titles(session, release)

        # Add new titles
        for title_data in titles_data:
            self._upsert_release_alternate_title(session, release, title_data)

        self.logger.debug(
            f"Replaced alternate titles for release {release.id} with {len(titles_data)} new titles")
