# extractors/relationships/base_relationship_extractor.py
from abc import abstractmethod
from typing import Dict, List, Any
from ..base_extractor import BaseExtractor


class BaseRelationshipExtractor(BaseExtractor):
    """Abstract base class for relationship extractors"""

    def __init__(self, ddex_data: Dict, entity_maps: Dict[str, Dict]):
        super().__init__(ddex_data)
        self.entity_maps = entity_maps

    @abstractmethod
    def extract(self) -> List[Dict[str, Any]]:
        """Extract relationship data with resolved entity references"""
        pass

    def _resolve_entity(self, entity_type: str, business_key: str):
        """Resolve business key to entity object"""
        entity_map = self.entity_maps.get(entity_type, {})
        entity = entity_map.get(business_key)

        if not entity:
            self.logger.warning(
                f"Could not resolve {entity_type} with key '{business_key}'"
            )
        return entity
