# extractors/entities/publisher_extractor.py

from typing import List, Dict, Any
from ..base_extractor import BaseExtractor


class PublisherExtractor(BaseExtractor):
    """Extract publisher data from DDEX JSON with SQLAlchemy architecture"""

    def extract(self) -> List[Dict[str, Any]]:
        """DEPRECATED: Use extract_core_data() instead"""
        return self.extract_core_data()

    def extract_core_data(self) -> List[Dict[str, Any]]:
        """Extract CORE publisher data only (no relationships)"""
        self.logger.debug("Extracting publishers")
        publishers = set()

        # Extract from sound recordings
        sound_recordings = self._safe_get(
            self.ddex_data, 'ResourceList.SoundRecording', [])
        for recording in self._ensure_list(sound_recordings):
            publishers.update(self._extract_from_recording(recording))

        # Also extract from releases if they have publisher information
        releases = self._safe_get(self.ddex_data, 'ReleaseList.Release', [])
        for release in self._ensure_list(releases):
            if release.get('@IsMainRelease') == 'true':
                publishers.update(self._extract_from_release(release))

        # Convert set to list of dictionaries
        publisher_list = [{'name': name} for name in publishers if name]
        self.logger.info(f"Extracted {len(publisher_list)} unique publishers")

        return publisher_list

    def extract_metadata(self) -> Dict[str, List[Dict[str, Any]]]:
        """Extract relationship/metadata separately for Phase 2 processing"""
        # Publishers don't have metadata relationships in this system
        # Return empty dict to maintain consistency with the pattern
        return {
            'aliases': [],          # Could add if you have publisher aliases
            'external_ids': []      # Could add if you have external publisher IDs
        }

    def _extract_from_recording(self, recording: Dict) -> set:
        """Extract publishers from sound recording"""
        publishers = set()

        # Get recording details (handle both dict and list structures)
        recording_details = self._safe_get(
            recording, 'SoundRecordingDetailsByTerritory', {}
        )
        if isinstance(recording_details, list):
            recording_details = recording_details[0] if recording_details else {
            }

        # Extract indirect resource contributors
        indirect_contributors = self._safe_get(
            recording_details, 'IndirectResourceContributor', []
        )

        for contributor in self._ensure_list(indirect_contributors):
            name = self._extract_publisher_name(contributor)
            if not name:
                continue

            contributor_roles = self._safe_get(
                contributor, 'IndirectResourceContributorRole', []
            )

            # Only process contributors that have 'publisher' in their role
            if not self._has_publisher_role(contributor_roles):
                continue

            publishers.add(name)

        # Extract direct resource contributors
        direct_contributors = self._safe_get(
            recording_details, 'ResourceContributor', []
        )

        for contributor in self._ensure_list(direct_contributors):
            name = self._extract_publisher_name(contributor)
            if not name:
                continue

            contributor_roles = self._safe_get(
                contributor, 'ResourceContributorRole', []
            )

            # Only process contributors that have 'publisher' in their role
            if not self._has_publisher_role(contributor_roles):
                continue

            publishers.add(name)

        # Also extract from general Contributor field
        general_contributors = self._safe_get(
            recording_details, 'Contributor', [])
        for contributor in self._ensure_list(general_contributors):
            name = self._extract_publisher_name(contributor)
            if not name:
                continue

            contributor_roles = self._safe_get(
                contributor, 'ContributorRole', [])

            # Only process contributors that have 'publisher' in their role
            if not self._has_publisher_role(contributor_roles):
                continue

            publishers.add(name)

        return publishers

    def _extract_from_release(self, release: Dict) -> set:
        """Extract publishers from release (if any publisher info exists at release level)"""
        publishers = set()

        # Get release details (handle both dict and list structures)
        release_details = self._safe_get(
            release, 'ReleaseDetailsByTerritory', {}
        )
        if isinstance(release_details, list):
            release_details = release_details[0] if release_details else {}

        # Extract contributors at release level
        contributors = self._safe_get(release_details, 'Contributor', [])
        for contributor in self._ensure_list(contributors):
            name = self._extract_publisher_name(contributor)
            if not name:
                continue

            contributor_roles = self._safe_get(
                contributor, 'ContributorRole', [])

            # Only process contributors that have 'publisher' in their role
            if not self._has_publisher_role(contributor_roles):
                continue

            publishers.add(name)

        return publishers

    def _extract_publisher_name(self, publisher_data: Dict) -> str:
        """Extract publisher name from various possible structures"""
        # Try different possible name fields
        name_candidates = [
            self._safe_get(publisher_data, 'PartyName.FullName'),
            self._safe_get(publisher_data, 'ContributorName'),
            self._safe_get(publisher_data, 'PartyName.DisplayName'),
            self._safe_get(publisher_data, 'PublisherName'),
            self._safe_get(publisher_data, 'Name')
        ]

        # Return the first non-empty name found
        for name in name_candidates:
            if name and isinstance(name, str) and name.strip():
                return name.strip()

        return None

    def _has_publisher_role(self, roles) -> bool:
        """Check if any role contains 'publisher'"""
        for role in self._ensure_list(roles):
            if role and isinstance(role, str) and 'publisher' in role.lower():
                return True
        return False

    def get_business_key(self, entity_data: Dict) -> str:
        """Generate business key for publisher"""
        return entity_data['name']
