# extractors/entities/person_extractor.py

from typing import List, Dict, Any
from ..base_extractor import BaseExtractor


class PersonExtractor(BaseExtractor):
    """Extract person data from DDEX JSON with SQLAlchemy architecture"""

    def extract(self) -> List[Dict[str, Any]]:
        """DEPRECATED: Use extract_core_data() instead"""
        return self.extract_core_data()

    def extract_core_data(self) -> List[Dict[str, Any]]:
        """Extract CORE person data only (no relationships)"""
        self.logger.debug("Extracting people")
        people = set()

        # Extract from sound recordings
        sound_recordings = self._safe_get(
            self.ddex_data, 'ResourceList.SoundRecording', [])
        for recording in self._ensure_list(sound_recordings):
            people.update(self._extract_from_recording(recording))

        # Convert set to list of dictionaries
        person_list = [{'name': name} for name in people if name]
        self.logger.debug(f"Extracted {len(person_list)} unique people")

        return person_list

    def extract_metadata(self) -> Dict[str, List[Dict[str, Any]]]:
        """Extract relationship/metadata separately for Phase 2 processing"""
        # People don't have metadata relationships in this system
        # Return empty dict to maintain consistency with the pattern
        return {
            'aliases': [],          # Could add if you have person aliases
            'external_ids': []      # Could add if you have external person IDs
        }

    def _extract_from_recording(self, recording: Dict) -> set:
        """Extract contributors from sound recording"""
        contributors = set()

        # Get recording details (handle both dict and list structures)
        recording_details = self._safe_get(
            recording, 'SoundRecordingDetailsByTerritory', {}
        )
        if isinstance(recording_details, list):
            recording_details = recording_details[0] if recording_details else {
            }

        # Extract indirect resource contributors
        indirect_contributors = self._safe_get(
            recording_details, 'IndirectResourceContributor', []
        )

        for indirect_contributor in self._ensure_list(indirect_contributors):
            name = self._extract_person_name(indirect_contributor)
            if not name:
                continue

            contributor_roles = self._safe_get(
                indirect_contributor, 'IndirectResourceContributorRole', []
            )

            # Check if any role contains 'publisher' - if so, skip this contributor
            if self._has_publisher_role(contributor_roles):
                self.logger.debug(f"Skipping {name} - has publisher role")
                continue

            contributors.add(name)

        # Extract direct resource contributors
        direct_contributors = self._safe_get(
            recording_details, 'ResourceContributor', []
        )

        for direct_contributor in self._ensure_list(direct_contributors):
            name = self._extract_person_name(direct_contributor)
            if not name:
                continue

            contributor_roles = self._safe_get(
                direct_contributor, 'ResourceContributorRole', []
            )

            # Check if any role contains 'publisher' - if so, skip this contributor
            if self._has_publisher_role(contributor_roles):
                self.logger.debug(f"Skipping {name} - has publisher role")
                continue

            contributors.add(name)

        # Also extract from general Contributor field
        general_contributors = self._safe_get(
            recording_details, 'Contributor', [])
        for contributor in self._ensure_list(general_contributors):
            name = self._extract_person_name(contributor)
            if not name:
                continue

            contributor_roles = self._safe_get(
                contributor, 'ContributorRole', [])

            # Check if any role contains 'publisher' - if so, skip this contributor
            if self._has_publisher_role(contributor_roles):
                self.logger.debug(f"Skipping {name} - has publisher role")
                continue

            contributors.add(name)

        return contributors

    def _extract_person_name(self, person_data: Dict) -> str:
        """Extract person name from various possible structures"""
        # Try different possible name fields
        name_candidates = [
            self._safe_get(person_data, 'PartyName.FullName'),
            self._safe_get(person_data, 'ContributorName'),
            self._safe_get(person_data, 'PartyName.DisplayName'),
            self._safe_get(person_data, 'PersonName.FullName'),
            self._safe_get(person_data, 'Name')
        ]

        # Return the first non-empty name found
        for name in name_candidates:
            if name and isinstance(name, str) and name.strip():
                return name.strip()

        return None

    def _has_publisher_role(self, roles) -> bool:
        """Check if any role contains 'publisher'"""
        for role in self._ensure_list(roles):
            if role and isinstance(role, str) and 'publisher' in role.lower():
                return True
        return False

    def get_business_key(self, entity_data: Dict) -> str:
        """Generate business key for person"""
        return entity_data['name']
