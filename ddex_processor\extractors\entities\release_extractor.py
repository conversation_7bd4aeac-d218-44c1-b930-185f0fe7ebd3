from typing import List, Dict, Any

# Remove SQLModel imports - we'll return plain data instead
from ..base_extractor import BaseExtractor


class ReleaseExtractor(BaseExtractor):
    """Extract release data from DDEX JSON with SQLAlchemy architecture"""

    def extract(self) -> List[Dict[str, Any]]:
        """DEPRECATED: Use extract_core_data() instead"""
        return self.extract_core_data()

    def extract_core_data(self) -> List[Dict[str, Any]]:
        """Extract CORE release data only (no relationships)"""
        releases = self._safe_get(self.ddex_data, 'ReleaseList.Release', [])
        extracted_releases = []

        for release in self._ensure_list(releases):
            if release.get('@IsMainRelease') == 'true':
                extracted_releases.append(
                    self._extract_core_release_data(release))

        return extracted_releases

    def extract_metadata(self) -> Dict[str, List[Dict[str, Any]]]:
        """Extract relationship/metadata separately for Phase 2 processing"""
        releases = self._safe_get(self.ddex_data, 'ReleaseList.Release', [])

        fuga_data = []
        alternate_titles_data = []

        for release in self._ensure_list(releases):
            if release.get('@IsMainRelease') == 'true':
                business_key = self._get_business_key_from_release(release)

                # Extract Fuga data
                fuga_id = self._safe_get(
                    release, 'ReleaseId.ProprietaryId.#text')
                if fuga_id:
                    fuga_data.append({
                        'release_business_key': business_key,
                        'fuga_id': fuga_id
                    })

                # Extract alternate titles data
                titles = self._safe_get(
                    release, 'ReleaseDetailsByTerritory.Title', [])
                for title in self._ensure_list(titles):
                    title_text = self._safe_get(title, 'TitleText')
                    title_type = self._safe_get(title, '@TitleType')
                    if title_text:
                        alternate_titles_data.append({
                            'release_business_key': business_key,
                            'title': title_text,
                            'title_type': title_type
                        })

        return {
            'fuga': fuga_data,
            'alternate_titles': alternate_titles_data
        }

    def _extract_core_release_data(self, release: Dict) -> Dict[str, Any]:
        """Extract ONLY core release fields (no relationships)"""
        upc = self._safe_get(release, 'ReleaseId.ICPN.#text')
        catalog_number = self._safe_get(
            release, 'ReleaseId.CatalogNumber.#text')
        title = self._safe_get(release, 'ReferenceTitle.TitleText')
        version = self._safe_get(release, 'ReferenceTitle.SubTitle', None)
        type = self._safe_get(release, 'ReleaseType')
        display_artist_name = self._safe_get(
            release, 'ReleaseDetailsByTerritory.DisplayArtistName')
        label_name = self._safe_get(
            release, 'ReleaseDetailsByTerritory.LabelName')
        parental_warning_type = self._safe_get(
            release, 'ReleaseDetailsByTerritory.ParentalWarningType')
        genre = self._safe_get(
            release, 'ReleaseDetailsByTerritory.Genre.GenreText')
        subgenre = self._safe_get(
            release, 'ReleaseDetailsByTerritory.Genre.SubGenre')
        release_date = self._safe_get(
            release, 'ReleaseDetailsByTerritory.ReleaseDate')
        pline_year = self._safe_get(release, 'PLine.Year')
        pline_text = self._safe_get(release, 'PLine.PLineText')
        cline_year = self._safe_get(release, 'CLine.Year')
        cline_text = self._safe_get(release, 'CLine.CLineText')

        # Return ONLY core fields - no relationships!
        return {
            'upc': upc,
            'catalog_number': catalog_number,
            'title': title,
            'version': version,
            'type': type,
            'display_artist_name': display_artist_name,
            'label_name': label_name,
            'parental_warning_type': parental_warning_type,
            'genre': genre,
            'subgenre': subgenre,
            'release_date': release_date,
            'pline_year': pline_year,
            'pline_text': pline_text,
            'cline_year': cline_year,
            'cline_text': cline_text,
        }

    def _get_business_key_from_release(self, release: Dict) -> str:
        """Generate business key from release dict"""
        upc = self._safe_get(release, 'ReleaseId.ICPN.#text')
        catalog_number = self._safe_get(
            release, 'ReleaseId.CatalogNumber.#text')
        return f"{upc}|{catalog_number}"

    def get_business_key(self, entity_data: Dict) -> str:
        """Generate business key from entity data dict"""
        return f"{entity_data['upc']}|{entity_data['catalog_number']}"

    # REMOVED: _extract_alternate_titles method - replaced with extract_metadata
