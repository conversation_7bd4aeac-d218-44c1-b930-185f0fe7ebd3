from typing import Dict
import json
import logging


from ddex_processor.config.settings import ProcessingConfig
from ddex_processor.db.connection_manager import get_database_manager
from ddex_processor.processors.core import (ReleaseProcessor, TrackProcessor, ArtistProcessor, RoleProcessor,
                                            PersonProcessor, PublisherProcessor, RightsControllerProcessor, TerritoryProcessor,
                                            DDEXFileProcessor)
from ddex_processor.processors.relations import (ReleaseArtistLinkProcessor, ReleaseTrackLinkProcessor,
                                                 TrackArtistLinkProcessor, TrackContributorLinkProcessor, TrackPublisherLinkProcessor,
                                                 RightsControllerLinkProcessor, TrackAudioLinksProcessor)
from ddex_processor.processors.commercial import DealProcessor
from ddex_processor.processors.metadata import ResourceFileProcessor
from ddex_processor.db.schema_manager import SchemaManager


class DDEXProcessor:
    """Main orchestrator for DDEX processing"""

    def __init__(self, config: ProcessingConfig, update_existing: bool = False):
        self.config = config
        # Use centralized database manager with optimized connection pooling
        self.db_manager = get_database_manager(config.connection_string)
        self.engine = self.db_manager.engine
        self.update_existing = update_existing
        self.logger = logging.getLogger(self.__class__.__name__)

        self.territory_processor = TerritoryProcessor(update_existing)
        self.role_processor = RoleProcessor(update_existing)
        self.artist_processor = ArtistProcessor(update_existing)
        self.person_processor = PersonProcessor(update_existing)
        self.publisher_processor = PublisherProcessor(update_existing)
        self.rights_controller_processor = RightsControllerProcessor(
            update_existing)
        self.release_processor = ReleaseProcessor(update_existing)
        self.track_processor = TrackProcessor(update_existing)
        self.ddex_file_processor = DDEXFileProcessor(update_existing)
        self.deal_processor = DealProcessor(update_existing)
        self.resource_file_processor = ResourceFileProcessor(update_existing)

        self.release_track_link_processor = ReleaseTrackLinkProcessor(
            update_existing)
        self.release_artist_link_processor = ReleaseArtistLinkProcessor(
            update_existing)
        self.track_artist_link_processor = TrackArtistLinkProcessor(
            update_existing)
        self.track_contributor_link_processor = TrackContributorLinkProcessor(
            update_existing)
        self.track_publisher_link_processor = TrackPublisherLinkProcessor(
            update_existing)
        self.rights_controller_link_processor = RightsControllerLinkProcessor(
            update_existing)
        self.track_audio_links_processor = TrackAudioLinksProcessor(
            update_existing)

    def process_ddex_json(self, ddex_data: Dict) -> bool:
        """Process DDEX JSON data using optimized multi-session approach"""
        try:
            # Phase 1: Process all core entities independently with shorter sessions
            entity_maps = self._process_core_entities(ddex_data)
            if not entity_maps:
                return False

            # Phase 2: Process metadata and relationships
            return self._process_relationships(ddex_data, entity_maps)

        except Exception as e:
            self.logger.error(f"Error processing DDEX JSON: {str(e)}")
            return False

    def _process_core_entities(self, ddex_data: Dict) -> Dict:
        """Process core entities in smaller session chunks for better connection management"""
        self.logger.info("Phase 1: Processing core entities")
        entity_maps = {}

        try:
            # Process basic reference data first (small, fast operations)
            session = self.db_manager.get_session()
            try:
                entity_maps['territories'] = self.territory_processor.process_territories(
                    session, ddex_data)
                entity_maps['roles'] = self.role_processor.process_roles(
                    session, ddex_data)
                session.commit()
                self.logger.debug("Reference data processed and committed")
            finally:
                session.close()

            # Process people and organizations (medium operations)
            session = self.db_manager.get_session()
            try:
                entity_maps['artists'] = self.artist_processor.process_artists(
                    session, ddex_data)
                entity_maps['persons'] = self.person_processor.process_persons(
                    session, ddex_data)
                entity_maps['publishers'] = self.publisher_processor.process_publishers(
                    session, ddex_data)
                entity_maps['rights_controllers'] = self.rights_controller_processor.process_rights_controllers(session, ddex_data)  # noqa: E501
                session.commit()
                self.logger.debug(
                    "People and organizations processed and committed")
            finally:
                session.close()

            # Process main content entities (larger operations)
            session = self.db_manager.get_session()
            try:
                entity_maps['releases'] = self.release_processor.process_releases(
                    session, ddex_data)
                entity_maps['tracks'] = self.track_processor.process_tracks(
                    session, ddex_data)
                entity_maps['ddex_files'] = self.ddex_file_processor.process_ddex_file(
                    session, ddex_data)
                session.commit()
                self.logger.debug(
                    "Main content entities processed and committed")
            finally:
                session.close()

            # Process deals and resource files (complex operations)
            session = self.db_manager.get_session()
            try:
                deal_group_map, deal_map = self.deal_processor.process_deals(
                    session, ddex_data, entity_maps['releases']
                )
                entity_maps['resource_files'] = self.resource_file_processor.process_resource_files(
                    session, ddex_data, entity_maps['ddex_files']
                )
                entity_maps['deal_groups'] = deal_group_map
                entity_maps['deals'] = deal_map
                session.commit()
                self.logger.debug(
                    "Deals and resource files processed and committed")
            finally:
                session.close()

            self.logger.info("Phase 1: Core entities complete")
            return entity_maps

        except Exception as e:
            self.logger.error(f"Error in core entities processing: {str(e)}")
            return {}

    def _process_relationships(self, ddex_data: Dict, entity_maps: Dict) -> bool:
        """Process metadata and relationships in optimized session chunks"""
        self.logger.info("Phase 2: Processing relationships")

        try:
            # Process metadata relationships
            session = self.db_manager.get_session()
            try:
                self.release_processor.process_release_metadata(
                    session, ddex_data, entity_maps['releases']
                )
                self.track_processor.process_track_metadata(
                    session, ddex_data, entity_maps['tracks']
                )
                self.deal_processor.process_deal_metadata(
                    session, ddex_data, entity_maps['deals']
                )
                session.commit()
                self.logger.debug(
                    "Metadata relationships processed and committed")
            finally:
                session.close()

            # Process many-to-many link relationships in separate session
            session = self.db_manager.get_session()
            try:
                success = (
                    self.release_track_link_processor.process_release_track_links(
                        session, ddex_data, entity_maps
                    ) and
                    self.release_artist_link_processor.process_release_artist_links(
                        session, ddex_data, entity_maps
                    ) and self.track_artist_link_processor.process_track_artist_links(
                        session, ddex_data, entity_maps
                    ) and self.track_contributor_link_processor.process_track_contributor_links(
                        session, ddex_data, entity_maps
                    ) and self.track_publisher_link_processor.process_track_publisher_links(
                        session, ddex_data, entity_maps
                    ) and self.rights_controller_link_processor.process_rights_controller_links(
                        session, ddex_data, entity_maps
                    ) and self.track_audio_links_processor.process_track_audio_links(
                        session, ddex_data, entity_maps
                    )
                )

                if not success:
                    session.rollback()
                    self.logger.error("Failed to process link relationships")
                    return False

                session.commit()
                self.logger.debug("Link relationships processed and committed")
            finally:
                session.close()

            self.logger.info("Phase 2: Relationships complete")
            return True

        except Exception as e:
            self.logger.error(f"Error in relationships processing: {str(e)}")
            return False


if __name__ == "__main__":
    # Configuration
    CONNECTION_STRING = "*************************************************************/orbital_catalogue_test"
    JSON_FILE_PATH = "data/ddex_sample.json"  # Path to your JSON file

    config = ProcessingConfig(
        CONNECTION_STRING
    )
    processor = DDEXProcessor(config, update_existing=True)

    # Schema management
    schema_manager = SchemaManager(CONNECTION_STRING)
    schema_manager.create_all_schemas(drop_existing=True)

    if not schema_manager.verify_schema():
        raise ValueError("Schema verification failed")

    # Load JSON data
    with open(JSON_FILE_PATH, 'r', encoding='utf-8') as f:
        ddex_data = json.load(f)

    processor.process_ddex_json(ddex_data)
