# processors/core/artist_processor.py

from typing import Dict, List
from sqlalchemy.orm import Session
import logging

# Import SQLAlchemy models (from generated file)
from ddex_processor.models.generated import Artist
from ddex_processor.extractors.entities.artist_extractor import ArtistExtractor
from ddex_processor.db.batch_operations import BatchProcessor


class ArtistProcessor:
    """SQLAlchemy-based processor for Artist entities"""

    def __init__(self, update_existing: bool = False):
        self.update_existing = update_existing
        self.logger = logging.getLogger(self.__class__.__name__)

    def process_artists(self, session: Session, ddex_data: Dict) -> Dict[str, Artist]:
        """Process core artist entities only (Phase 1)"""
        self.logger.debug("Processing artists...")

        extractor = ArtistExtractor(ddex_data)
        artist_data_list = extractor.extract_core_data()

        # Use batch processing for better performance with datasets > 3 entities
        if len(artist_data_list) > 3:  # Lower threshold for more aggressive batch processing
            return self._process_artists_batch(session, artist_data_list, extractor)
        else:
            # Use individual processing for very small datasets
            return self._process_artists_individual(session, artist_data_list, extractor)

    def _process_artists_individual(self, session: Session, artist_data_list: List[Dict], extractor: ArtistExtractor) -> Dict[str, Artist]:
        """Process artists individually (original method)"""
        entity_map = {}

        for artist_data in artist_data_list:
            business_key = extractor.get_business_key(artist_data)
            artist = self._upsert_artist(session, artist_data)
            entity_map[business_key] = artist

        self.logger.debug(f"Processed {len(entity_map)} artists individually")
        return entity_map

    def _process_artists_batch(self, session: Session, artist_data_list: List[Dict], extractor: ArtistExtractor) -> Dict[str, Artist]:
        """Process artists using batch operations for better performance"""
        batch_processor = BatchProcessor(session)

        # Perform bulk upsert using name as conflict column
        results = batch_processor.bulk_upsert(
            model_class=Artist,
            data_list=artist_data_list,
            conflict_columns=['name'],  # Artists are unique by name
            # Only update name if needed (artists rarely change)
            update_columns=['name']
        )

        # Build entity map using business keys
        entity_map = {}
        for i, artist_data in enumerate(artist_data_list):
            business_key = extractor.get_business_key(artist_data)
            if i < len(results):
                # Map result back to business key
                entity_map[business_key] = Artist(
                    id=results[i].id,
                    name=results[i].name
                )

        self.logger.debug(f"Batch processed {len(entity_map)} artists")
        return entity_map

    def _upsert_artist(self, session: Session, artist_data: Dict) -> Artist:
        """Upsert an artist using name lookup"""
        # Find existing artist by business key (name)
        existing = session.query(Artist).filter_by(
            name=artist_data['name']
        ).first()

        if existing and self.update_existing:
            # Update existing artist (though artists rarely change)
            self.logger.debug(f"Updating existing artist: {existing.name}")
            for key, value in artist_data.items():
                if hasattr(existing, key) and key != 'id':
                    setattr(existing, key, value)
            return existing

        elif existing:
            # Skip update, return existing
            self.logger.debug(f"Skipping existing artist: {existing.name}")
            return existing

        else:
            # Create new artist
            self.logger.debug(
                f"Creating new artist: {artist_data.get('name', 'Unknown')}")
            new_artist = Artist(**artist_data)
            session.add(new_artist)
            return new_artist

    def get_artist_by_name(self, session: Session, name: str) -> Artist:
        """Get an artist by name"""
        try:
            return session.query(Artist).filter_by(name=name).first()
        except Exception as e:
            self.logger.error(
                f"Error getting artist by name '{name}': {str(e)}")
            return None

    def get_all_artists(self, session: Session) -> List[Artist]:
        """Get all artists"""
        try:
            return session.query(Artist).order_by(Artist.name).all()
        except Exception as e:
            self.logger.error(f"Error getting all artists: {str(e)}")
            return []

    def search_artists(self, session: Session, search_term: str) -> List[Artist]:
        """Search artists by name (case-insensitive)"""
        try:
            return session.query(Artist).filter(
                Artist.name.ilike(f"%{search_term}%")
            ).order_by(Artist.name).all()
        except Exception as e:
            self.logger.error(f"Error searching artists: {str(e)}")
            return []

    def validate_artist_data(self, artist_data: Dict) -> bool:
        """Validate that artist data has all required fields"""
        if 'name' not in artist_data or not artist_data['name']:
            self.logger.warning("Artist data missing required 'name' field")
            return False

        # Validate name is not just whitespace
        if not artist_data['name'].strip():
            self.logger.warning(
                "Artist name cannot be empty or just whitespace")
            return False

        # Check for reasonable name length
        if len(artist_data['name']) > 500:  # Adjust limit as needed
            self.logger.warning(
                f"Artist name too long: {len(artist_data['name'])} characters")
            return False

        return True

    def clean_artist_name(self, name: str) -> str:
        """Clean and normalize artist name"""
        if not name:
            return ""

        # Strip whitespace
        cleaned = name.strip()

        # Remove multiple consecutive spaces
        import re
        cleaned = re.sub(r'\s+', ' ', cleaned)

        return cleaned
