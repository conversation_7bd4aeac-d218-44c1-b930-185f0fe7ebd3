# db/schema_manager.py
"""
Database schema creation and management using SQLAlchemy Declarative Base
"""
from sqlalchemy import text, inspect
from ddex_processor.models.generated import Base  # Import your declarative base
from ddex_processor.db.connection_manager import get_database_manager
# Import all your models to ensure they're registered with Base.metadata
from ddex_processor.models.generated import (
    # Core models
    Release, Track, Artist, Person, Publisher,
    RightsController, Role, Territory, DDEXFile,
    # Metadata models
    ReleaseFuga, ReleaseAlternateTitles, ResourceFile,
    TrackFuga, TrackAlternateTitles,
    # Relationship models
    ReleaseArtistLinks, ReleaseTrackLinks,
    TrackArtistLinks,
    TrackContributorLinks, TrackPublisherLinks,
    # Commercial models
    DealGroup, Deal, DealPricing, DealTerritories, DealUsageTypes
)
import logging

logger = logging.getLogger(__name__)


class SchemaManager:
    def __init__(self, database_url: str):
        # Use centralized database manager with optimized connection pooling
        self.db_manager = get_database_manager(database_url)
        self.engine = self.db_manager.engine
        self.schemas = ['core', 'metadata', 'relations', 'commercial']

    def create_all_schemas(self, drop_existing: bool = False):
        """
        Create all schemas and tables using SQLAlchemy Declarative Base

        Args:
            drop_existing: If True, drops existing schemas first (dangerous!)
        """
        try:
            with self.engine.connect() as conn:
                # Enable UUID extension
                conn.execute(
                    text("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\""))
                conn.commit()

                # Create schemas first
                self._create_schemas(conn, drop_existing)

                # Create all tables using SQLAlchemy Declarative Base
                if drop_existing:
                    logger.warning("Dropping all existing tables...")
                    Base.metadata.drop_all(self.engine)

                logger.info("Creating all tables...")
                Base.metadata.create_all(self.engine)
                # ✅ Indexes are now created automatically with the tables!

                logger.info("Schema creation completed successfully")

        except Exception as e:
            logger.error(f"Error creating schemas: {e}")
            raise

    def _create_schemas(self, conn, drop_existing: bool = False):
        """Create PostgreSQL schemas"""
        for schema_name in self.schemas:
            try:
                if drop_existing:
                    conn.execute(
                        text(f"DROP SCHEMA IF EXISTS {schema_name} CASCADE"))

                conn.execute(
                    text(f"CREATE SCHEMA IF NOT EXISTS {schema_name}"))
                logger.info(f"Created schema: {schema_name}")

            except Exception as e:
                logger.error(f"Error creating schema {schema_name}: {e}")
                raise

        conn.commit()

    # ✅ No longer needed - indexes are defined in models
    # def _create_custom_indexes(self, conn):
    #     """Create custom indexes for performance"""
    #     # All indexes are now defined in the model __table_args__
    #     # This method can be used for database-specific optimizations if needed
    #     pass

    def verify_schema(self) -> bool:
        """Verify that all expected tables exist"""
        inspector = inspect(self.engine)

        expected_tables = {
            # ✅ Fixed: 'artist' not 'artists'
            'core': ['artist', 'ddex_file', 'person', 'publisher', 'release', 'rights_controller', 'role', 'territory', 'track'],
            'metadata': ['release_alternate_titles', 'release_fuga', 'resource_file', 'track_alternate_titles', 'track_fuga'],
            'relations': ['release_artist_links', 'release_track_links', 'track_artist_links', 'track_contributor_links', 'track_publisher_links', 'track_audio_links'],
            'commercial': ['deal_group', 'deal_pricing', 'deal_territory', 'deal_usage_type', 'deal']
        }

        missing_tables = []

        for schema_name, tables in expected_tables.items():
            existing_tables = inspector.get_table_names(schema=schema_name)

            for table in tables:
                if table not in existing_tables:
                    missing_tables.append(f"{schema_name}.{table}")

        if missing_tables:
            logger.error(f"Missing tables: {missing_tables}")
            return False

        logger.info("Schema verification passed - all expected tables exist")
        return True

    def get_table_info(self, schema_name: str = None) -> dict:
        """Get information about tables in the database"""
        inspector = inspect(self.engine)
        table_info = {}

        schemas_to_check = [schema_name] if schema_name else self.schemas

        for schema in schemas_to_check:
            tables = inspector.get_table_names(schema=schema)
            table_info[schema] = {}

            for table in tables:
                columns = inspector.get_columns(table, schema=schema)
                indexes = inspector.get_indexes(table, schema=schema)
                foreign_keys = inspector.get_foreign_keys(table, schema=schema)

                table_info[schema][table] = {
                    'columns': len(columns),
                    'indexes': len(indexes),
                    'foreign_keys': len(foreign_keys)
                }

        return table_info

    def debug_metadata(self):
        """Debug helper to see what tables are registered with Base.metadata"""
        logger.info("Tables registered with Base.metadata:")
        for table_name, table in Base.metadata.tables.items():
            logger.info(f"  {table_name} -> {table.schema}.{table.name}")

        logger.info(f"Total tables: {len(Base.metadata.tables)}")

    def create_tables_by_schema(self, schema_name: str):
        """Create tables for a specific schema only"""
        schema_tables = [table for table in Base.metadata.tables.values()
                         if table.schema == schema_name]

        if not schema_tables:
            logger.warning(f"No tables found for schema: {schema_name}")
            return

        logger.info(
            f"Creating {len(schema_tables)} tables for schema: {schema_name}")

        with self.engine.connect() as conn:
            # Create schema if it doesn't exist
            conn.execute(text(f"CREATE SCHEMA IF NOT EXISTS {schema_name}"))
            conn.commit()

        # Create only the tables for this schema
        for table in schema_tables:
            table.create(self.engine, checkfirst=True)
            logger.debug(f"Created table: {table.schema}.{table.name}")


# Usage example
if __name__ == "__main__":
    # Example usage
    DATABASE_URL = "postgresql://user:password@localhost:5432/your_db"

    schema_manager = SchemaManager(DATABASE_URL)

    # Debug what's registered
    schema_manager.debug_metadata()

    # Create all schemas and tables
    schema_manager.create_all_schemas(drop_existing=False)

    # Verify everything was created
    if schema_manager.verify_schema():
        print("✅ All schemas and tables created successfully!")
    else:
        print("❌ Schema verification failed!")

    # Get table info
    table_info = schema_manager.get_table_info()
    for schema, tables in table_info.items():
        print(f"\n{schema} schema:")
        for table, info in tables.items():
            print(
                f"  {table}: {info['columns']} columns, {info['indexes']} indexes")
