# -*- coding: utf-8 -*-

# Learn more: https://github.com/kennethreitz/setup.py

from setuptools import setup, find_packages
import os

# Get the long description from the README file
readme_path = os.path.join(os.path.dirname(__file__), 'README.rst')
if os.path.exists(readme_path):
    with open(readme_path, encoding='utf-8') as f:
        readme = f.read()
else:
    readme = 'A package for processing DDEX files into a relational database'

# Get the license
license_path = os.path.join(os.path.dirname(__file__), 'LICENSE')
if os.path.exists(license_path):
    with open(license_path, encoding='utf-8') as f:
        license_text = f.read()
else:
    license_text = 'Proprietary'

setup(
    name='ddex_processor',
    version='0.1.0',
    description='A package for processing DDEX files into a relational database',
    long_description=readme,
    long_description_content_type='text/x-rst',
    author='<PERSON>',
    author_email='<EMAIL>',
    url='https://github.com/Jopgood/ddex-processor',
    license=license_text,
    packages=find_packages(exclude=('tests', 'docs')),

    # Dependencies from your list
    install_requires=[
        'boto3==1.38.46',
        'botocore==1.38.46',
        'greenlet==3.2.3',
        'jmespath==1.0.1',
        'python-dateutil==2.9.0.post0',
        'python-dotenv==1.1.1',
        's3transfer==0.13.0',
        'six==1.17.0',
        'SQLAlchemy==2.0.41',
        'typing_extensions==4.14.0',
        'urllib3==2.5.0',
        'xmltodict==0.14.2',
    ],

    # Entry points for command line usage
    entry_points={
        'console_scripts': [
            'ddex-processor=ddex_processor.main:main',
        ],
    },

    # Additional metadata
    classifiers=[
        'Development Status :: 3 - Alpha',
        'Intended Audience :: Developers',
        'License :: Other/Proprietary License',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.12',
    ],
    python_requires='>=3.12',

    # Optional: include additional files
    include_package_data=True,
    zip_safe=False,
)
