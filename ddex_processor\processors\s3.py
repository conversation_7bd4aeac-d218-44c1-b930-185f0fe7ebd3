"""
S3-based DDEX file processor with database optimization
"""

import boto3
import xmltodict
import time
import gc
import os
from typing import Dict, Iterator, Optional, List
import logging
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from ddex_processor.config.settings import ProcessingConfig
from ddex_processor.processors.ddex import DDEXProcessor
from ddex_processor.db.connection_manager import get_database_manager


class S3Processor:
    """DDEX processor for S3-based file processing with database optimization"""

    def __init__(self, config: ProcessingConfig, update_existing: bool = False):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)

        # Use centralized database manager with optimized connection pooling
        self.db_manager = get_database_manager(config.connection_string)
        self.engine = self.db_manager.engine

        self.ddex_processor = DDEXProcessor(config, update_existing)

        # Initialize S3 client
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=os.getenv("ACCESS_KEY"),
            aws_secret_access_key=os.getenv("SECRET_KEY"),
            region_name='eu-west-2'
        )

        # Processing settings - optimized for connection pooling
        self.batch_size = config.batch_size if hasattr(
            config, 'batch_size') else 1
        # Removed artificial delay - connection pooling handles efficiency
        self.processing_delay = 0.0
        self.memory_cleanup_frequency = 25  # Less frequent cleanup for better performance

        # Concurrent processing settings
        # Use config values with updated defaults
        self.max_workers = getattr(config, 'max_workers', 4)
        self.enable_concurrent = getattr(config, 'enable_concurrent', True)

        # Statistics tracking
        self.stats = {
            'processed': 0,
            'failed': 0,
            'start_time': datetime.now()
        }

    def process_single_s3_file(self, bucket: str, key: str) -> bool:
        """Process a single specific S3 file by key"""

        self.logger.info(f"🚀 Processing single file: s3://{bucket}/{key}")

        try:
            # Validate file is processable
            if not self._is_processable_file(key):
                self.logger.error(
                    f"❌ File is not a processable DDEX file: {key}")
                return False

            # Process the file
            success = self._process_single_file(bucket, key)

            if success:
                self.logger.debug(f"✅ Successfully processed: {key}")
                self.stats['processed'] = 1
                self.stats['failed'] = 0
            else:
                self.logger.error(f"❌ Failed to process: {key}")
                self.stats['processed'] = 0
                self.stats['failed'] = 1

            # Log final result
            duration = datetime.now() - self.stats['start_time']
            self.logger.info(f"⏱️ Processing time: {duration}")

            return success

        except Exception as e:
            self.logger.error(f"Error processing single file {key}: {e}")
            return False

    def process_from_s3(self, bucket: str, prefix: str = '', limit: Optional[int] = None) -> bool:
        """Process DDEX files from S3 bucket"""

        self.logger.info("🚀 Starting S3 DDEX processing")
        self.logger.info(
            f"📊 Target: {limit or 'ALL'} files from s3://{bucket}/{prefix}")

        try:
            # Efficiently collect file keys with early termination
            file_keys = list(self._get_s3_file_keys(bucket, prefix, limit))
            total_files = len(file_keys)

            if total_files == 0:
                self.logger.warning("⚠️ No processable files found!")
                return True

            self.logger.info(f"📁 Ready to process {total_files} files")
            self._log_processing_settings()

            # Choose processing method based on configuration and file count
            if self.enable_concurrent and total_files > 5:
                return self._process_files_concurrent(bucket, file_keys)
            else:
                return self._process_files_sequential(bucket, file_keys)

        except Exception as e:
            self.logger.error(f"S3 batch processing failed: {e}")
            return False

    def _process_files_sequential(self, bucket: str, file_keys: List[str]) -> bool:
        """Process files sequentially (original method)"""
        total_files = len(file_keys)

        # Process files sequentially for memory efficiency
        for i, key in enumerate(file_keys):
            success = self._process_single_file(bucket, key)

            self._update_stats(success)
            self._log_progress_if_needed(i + 1, total_files)
            self._cleanup_memory_if_needed(i + 1)

            # Optional delay for CPU credit preservation (disabled by default with connection pooling)
            if self.processing_delay > 0:
                time.sleep(self.processing_delay)

        self._log_final_stats(total_files)
        return True

    def _process_files_concurrent(self, bucket: str, file_keys: List[str]) -> bool:
        """Process files concurrently using ThreadPoolExecutor"""
        total_files = len(file_keys)
        self.logger.info(
            f"🚀 Starting concurrent processing with {self.max_workers} workers")

        # Thread-safe statistics
        stats_lock = threading.Lock()

        def process_file_with_stats(key: str) -> bool:
            """Process a single file and update thread-safe stats"""
            success = self._process_single_file(bucket, key)

            with stats_lock:
                self._update_stats(success)
                processed_count = self.stats['processed'] + \
                    self.stats['failed']
                # Log every 25 files (reduced frequency)
                if processed_count % 25 == 0:
                    self.logger.info(
                        f"Progress: {processed_count}/{total_files} files processed")

            return success

        # Process files concurrently
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_key = {executor.submit(process_file_with_stats, key): key
                             for key in file_keys}

            # Process completed tasks
            for future in as_completed(future_to_key):
                key = future_to_key[future]
                try:
                    success = future.result()
                    if not success:
                        self.logger.warning(f"Failed to process file: {key}")
                except Exception as e:
                    self.logger.error(f"Exception processing file {key}: {e}")
                    with stats_lock:
                        self._update_stats(False)

        self._log_final_stats(total_files)
        return True

    def estimate_processing_time(self, bucket: str, prefix: str = '', sample_size: int = 10) -> Optional[float]:
        """Estimate processing time based on sample run"""

        self.logger.info("🧪 Running sample processing to estimate time...")

        start_time = datetime.now()
        success = self.process_from_s3(bucket, prefix, limit=sample_size)
        end_time = datetime.now()

        if success and self.stats['processed'] > 0:
            sample_duration = end_time - start_time
            files_per_hour = self.stats['processed'] / \
                sample_duration.total_seconds() * 3600

            self._log_time_estimates(files_per_hour)
            return files_per_hour

        self.logger.error("Sample processing failed - cannot estimate")
        return None

    def _process_single_file(self, bucket: str, key: str) -> bool:
        """Process a single S3 file with memory optimization"""

        try:
            self.logger.debug(f"📥 Processing {key}")

            # Download and convert file
            xml_content = self._download_s3_file(bucket, key)
            ddex_json = self._convert_xml_to_json(xml_content)
            del xml_content  # Immediate cleanup

            # Process with DDEXProcessor
            success = self.ddex_processor.process_ddex_json(ddex_json)
            del ddex_json  # Immediate cleanup

            if success:
                self.logger.debug(f"✅ Successfully processed {key}")
            else:
                self.logger.warning(f"❌ Failed to process {key}")

            return success

        except Exception as e:
            self.logger.error(f"Error processing {key}: {e}")
            return False
        finally:
            gc.collect()

    def _download_s3_file(self, bucket: str, key: str) -> str:
        """Download file content from S3"""
        response = self.s3_client.get_object(Bucket=bucket, Key=key)
        content = response['Body'].read().decode('utf-8')
        del response
        return content

    def _convert_xml_to_json(self, xml_content: str) -> Dict:
        """Convert XML to JSON with memory management"""

        xml_dict = xmltodict.parse(xml_content)

        # Extract DDEX message content
        ddex_message_types = [
            'NewReleaseMessage',
            'ERNReleaseByReleaseNotificationMessage'
        ]

        for message_type in ddex_message_types:
            if message_type in xml_dict:
                result = xml_dict[message_type]
                del xml_dict
                return result

        # Fallback: find any message with MessageHeader
        for key, value in xml_dict.items():
            if isinstance(value, dict) and 'MessageHeader' in value:
                result = value
                del xml_dict
                return result

        # Last resort: return the whole dict
        return xml_dict

    def _get_s3_file_keys(self, bucket: str, prefix: str, limit: Optional[int]) -> Iterator[str]:
        """Get S3 file keys with efficient pagination and early termination"""

        paginator = self.s3_client.get_paginator('list_objects_v2')
        page_iterator = paginator.paginate(
            Bucket=bucket,
            Prefix=prefix,
            PaginationConfig={'PageSize': 100}
        )

        valid_count = 0
        total_scanned = 0

        self.logger.info(
            f"🔍 Scanning S3 for processable files (limit: {limit or 'unlimited'})")

        for page in page_iterator:
            if 'Contents' not in page:
                continue

            for obj in page['Contents']:
                total_scanned += 1

                # Early termination: stop scanning if we have enough valid files
                if limit and valid_count >= limit:
                    self.logger.debug(
                        f"✅ Found {valid_count} files after scanning {total_scanned} objects")
                    return

                key = obj['Key']
                if self._is_processable_file(key):
                    valid_count += 1
                    self.logger.debug(f"Found valid file {valid_count}: {key}")
                    yield key

                # Log progress for large scans (reduced frequency)
                if total_scanned % 5000 == 0:
                    self.logger.info(
                        f"📊 Scanned {total_scanned} objects, found {valid_count} valid files")

        self.logger.info(
            f"✅ Scan complete: found {valid_count} files after scanning {total_scanned} objects")

    def _is_processable_file(self, key: str) -> bool:
        """Check if file should be processed"""
        return (key.lower().endswith('.xml') and
                'batchcomplete' not in key.lower())

    def _update_stats(self, success: bool):
        """Update processing statistics"""
        if success:
            self.stats['processed'] += 1
        else:
            self.stats['failed'] += 1

    def _log_progress_if_needed(self, current: int, total: int):
        """Log progress every 100 files (reduced frequency)"""
        if current % 100 == 0:
            self._log_progress(current, total)

    def _cleanup_memory_if_needed(self, current: int):
        """Force garbage collection periodically"""
        if current % self.memory_cleanup_frequency == 0:
            collected = gc.collect()
            if collected > 0:
                self.logger.debug(f"🗑️ Cleaned up {collected} objects")

    def _log_processing_settings(self):
        """Log current processing configuration"""
        self.logger.debug("⚙️ Processing settings:")
        self.logger.debug(f"   - Batch size: {self.batch_size}")
        self.logger.debug(f"   - Processing delay: {self.processing_delay}s")
        self.logger.debug(
            f"   - Memory cleanup frequency: {self.memory_cleanup_frequency}")
        self.logger.debug(
            f"   - Concurrent processing: {self.enable_concurrent}")
        self.logger.debug(f"   - Max workers: {self.max_workers}")

    def _log_progress(self, current: int, total: int):
        """Log detailed progress information"""
        elapsed = datetime.now() - self.stats['start_time']
        rate = current / elapsed.total_seconds() * 3600  # files per hour
        percentage = (current / total) * 100

        self.logger.info(
            f"📊 Progress: {current}/{total} ({percentage:.1f}%) - Rate: {rate:.1f} files/hour")
        self.logger.debug(
            f"✅ Success: {self.stats['processed']}, ❌ Failed: {self.stats['failed']}")

    def _log_final_stats(self, total_files: int):
        """Log comprehensive final statistics"""
        end_time = datetime.now()
        duration = end_time - self.stats['start_time']
        rate = total_files / duration.total_seconds() * 3600
        success_rate = (self.stats['processed'] /
                        total_files) * 100 if total_files > 0 else 0

        self.logger.info("=" * 60)
        self.logger.info("🎉 PROCESSING COMPLETE")
        self.logger.info("=" * 60)
        self.logger.info(f"📁 Total files: {total_files}")
        self.logger.info(
            f"✅ Successfully processed: {self.stats['processed']}")
        self.logger.info(f"❌ Failed: {self.stats['failed']}")
        self.logger.info(f"📊 Success rate: {success_rate:.1f}%")
        self.logger.info(f"⏱️ Duration: {duration}")
        self.logger.info(f"⚡ Processing rate: {rate:.1f} files/hour")

    def _log_time_estimates(self, files_per_hour: float):
        """Log processing time estimates"""
        estimated_hours_43k = 43000 / files_per_hour
        estimated_days = estimated_hours_43k / 24

        self.logger.info("📈 PROCESSING TIME ESTIMATE")
        self.logger.info(f"Sample rate: {files_per_hour:.1f} files/hour")
        self.logger.info(
            f"43k files: {estimated_hours_43k:.1f} hours ({estimated_days:.1f} days)")
        self.logger.info(
            "💡 Consider upgrading instance type for better performance")
