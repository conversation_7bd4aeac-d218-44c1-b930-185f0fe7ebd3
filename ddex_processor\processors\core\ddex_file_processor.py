# processors/core/ddex_file.py

from typing import Dict
from sqlalchemy.orm import Session
import logging

from ddex_processor.models.generated import DDEXFile
from ddex_processor.extractors.entities.ddex_file_extractor import DDEXFileExtractor


class DDEXFileProcessor:
    """SQLAlchemy-based processor for DDEX file metadata"""

    def __init__(self, update_existing: bool = False):
        self.update_existing = update_existing
        self.logger = logging.getLogger(self.__class__.__name__)

    def process_ddex_file(self, session: Session, ddex_data: Dict) -> DDEXFile:
        """Process DDEX file metadata"""
        self.logger.debug("Processing DDEX file metadata...")

        extractor = DDEXFileExtractor(ddex_data)
        ddex_file_data = extractor.extract_core_data()[0]

        # Find existing DDEX file by business key (filename)
        existing = session.query(DDEXFile).filter_by(
            message_filename=ddex_file_data['message_filename'], message_id=ddex_file_data['message_id']
        ).first()

        if existing and self.update_existing:
            # Update existing DDEX file
            self.logger.debug(
                f"Updating existing DDEX file: {existing.message_filename}")
            for key, value in ddex_file_data.items():
                if hasattr(existing, key) and key != 'id':
                    setattr(existing, key, value)
            return existing

        elif existing:
            # Skip update, return existing
            self.logger.debug(
                f"Skipping existing DDEX file: {existing.message_filename}")
            return existing

        else:
            # Create new DDEX file
            self.logger.debug(
                f"Creating new DDEX file: {ddex_file_data['message_filename']}")
            new_ddex_file = DDEXFile(**ddex_file_data)
            session.add(new_ddex_file)
            return new_ddex_file
